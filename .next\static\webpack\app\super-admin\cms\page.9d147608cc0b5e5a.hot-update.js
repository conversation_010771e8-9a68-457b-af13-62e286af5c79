"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/super-admin/cms/page",{

/***/ "(app-pages-browser)/./app/super-admin/cms/page.tsx":
/*!**************************************!*\
  !*** ./app/super-admin/cms/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CMSPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panels-top-left.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CMSPage() {\n    var _session_user, _content_features, _content_features1, _content_features2, _content_features3, _content_features_items, _content_features4, _content_testimonials, _content_testimonials1, _content_testimonials2, _content_testimonials3, _content_testimonials_items, _content_testimonials4, _content_faq, _content_faq1, _content_faq2, _content_faq3, _content_faq_items, _content_faq4, _content_cta, _content_cta1, _content_cta2, _content_cta3, _content_cta4, _content_cta5, _content_cta6, _content_seo, _content_seo1, _content_seo2, _content_seo3;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        hero: {\n            enabled: true,\n            title: \"Build Your SaaS Business\",\n            subtitle: \"The Complete Platform\",\n            description: \"Everything you need to launch, grow, and scale your SaaS business. From customer management to billing, we've got you covered.\",\n            primaryCTA: {\n                text: \"Start Free Trial\",\n                link: \"/auth/signup\"\n            },\n            secondaryCTA: {\n                text: \"Watch Demo\",\n                link: \"/demo\"\n            },\n            backgroundImage: \"\",\n            backgroundVideo: \"\"\n        },\n        features: {\n            enabled: true,\n            title: \"Everything You Need\",\n            subtitle: \"Powerful Features\",\n            items: [\n                {\n                    id: \"1\",\n                    title: \"Customer Management\",\n                    description: \"Manage your customers, track interactions, and build lasting relationships.\",\n                    icon: \"users\",\n                    image: \"\"\n                },\n                {\n                    id: \"2\",\n                    title: \"Subscription Billing\",\n                    description: \"Automated billing, invoicing, and payment processing for recurring revenue.\",\n                    icon: \"credit-card\",\n                    image: \"\"\n                },\n                {\n                    id: \"3\",\n                    title: \"Analytics & Reports\",\n                    description: \"Comprehensive analytics to track your business performance and growth.\",\n                    icon: \"bar-chart\",\n                    image: \"\"\n                }\n            ]\n        },\n        pricing: {\n            enabled: true,\n            title: \"Simple, Transparent Pricing\",\n            subtitle: \"Choose the plan that fits your needs\",\n            showPricingTable: true,\n            customMessage: \"\"\n        },\n        testimonials: {\n            enabled: true,\n            title: \"What Our Customers Say\",\n            subtitle: \"Trusted by thousands of businesses\",\n            items: [\n                {\n                    id: \"1\",\n                    name: \"John Smith\",\n                    role: \"CEO\",\n                    company: \"TechCorp\",\n                    content: \"This platform has transformed how we manage our SaaS business. Highly recommended!\",\n                    avatar: \"\",\n                    rating: 5\n                }\n            ]\n        },\n        faq: {\n            enabled: true,\n            title: \"Frequently Asked Questions\",\n            subtitle: \"Everything you need to know\",\n            items: [\n                {\n                    id: \"1\",\n                    question: \"How do I get started?\",\n                    answer: \"Simply sign up for a free trial and follow our onboarding guide to set up your account.\"\n                },\n                {\n                    id: \"2\",\n                    question: \"Can I cancel anytime?\",\n                    answer: \"Yes, you can cancel your subscription at any time. No long-term contracts or cancellation fees.\"\n                }\n            ]\n        },\n        cta: {\n            enabled: true,\n            title: \"Ready to Get Started?\",\n            description: \"Join thousands of businesses already using our platform to grow their SaaS.\",\n            buttonText: \"Start Your Free Trial\",\n            buttonLink: \"/auth/signup\",\n            backgroundImage: \"\"\n        },\n        footer: {\n            enabled: true,\n            companyDescription: \"The complete SaaS platform for modern businesses.\",\n            links: [\n                {\n                    id: \"1\",\n                    title: \"Product\",\n                    items: [\n                        {\n                            id: \"1\",\n                            text: \"Features\",\n                            link: \"/features\"\n                        },\n                        {\n                            id: \"2\",\n                            text: \"Pricing\",\n                            link: \"/pricing\"\n                        },\n                        {\n                            id: \"3\",\n                            text: \"Security\",\n                            link: \"/security\"\n                        }\n                    ]\n                },\n                {\n                    id: \"2\",\n                    title: \"Company\",\n                    items: [\n                        {\n                            id: \"1\",\n                            text: \"About\",\n                            link: \"/about\"\n                        },\n                        {\n                            id: \"2\",\n                            text: \"Blog\",\n                            link: \"/blog\"\n                        },\n                        {\n                            id: \"3\",\n                            text: \"Careers\",\n                            link: \"/careers\"\n                        }\n                    ]\n                }\n            ],\n            socialLinks: {\n                twitter: \"\",\n                linkedin: \"\",\n                facebook: \"\",\n                instagram: \"\"\n            },\n            copyrightText: \"\\xa9 2024 Your Company. All rights reserved.\"\n        },\n        seo: {\n            title: \"SaaS Platform - Build Your Business\",\n            description: \"The complete SaaS platform for modern businesses. Customer management, billing, analytics, and more.\",\n            keywords: \"saas, platform, business, customer management, billing, analytics\",\n            ogImage: \"\"\n        }\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewMode, setPreviewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                lineNumber: 285,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n            lineNumber: 284,\n            columnNumber: 7\n        }, this);\n    }\n    if (status === \"unauthenticated\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/auth/signin\");\n    }\n    if ((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) !== \"SUPER_ADMIN\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/dashboard\");\n    }\n    const fetchContent = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/super-admin/cms\");\n            const data = await response.json();\n            if (data.success) {\n                setContent({\n                    ...content,\n                    ...data.content\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching CMS content:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Failed to load CMS content\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveContent = async ()=>{\n        try {\n            setSaving(true);\n            const response = await fetch(\"/api/super-admin/cms\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(content)\n            });\n            const data = await response.json();\n            if (data.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"CMS content saved successfully\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_11__.toast.error(data.error || \"Failed to save CMS content\");\n            }\n        } catch (error) {\n            console.error(\"Error saving CMS content:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Failed to save CMS content\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchContent();\n    }, []);\n    const updateContent = (section, key, value)=>{\n        setContent((prev)=>({\n                ...prev,\n                [section]: {\n                    ...prev[section],\n                    [key]: value\n                }\n            }));\n    };\n    // Feature management functions\n    const addFeature = ()=>{\n        const newFeature = {\n            id: Date.now().toString(),\n            title: \"New Feature\",\n            description: \"Feature description\",\n            icon: \"star\",\n            image: \"\"\n        };\n        setContent((prev)=>{\n            var _prev_features;\n            return {\n                ...prev,\n                features: {\n                    ...prev.features,\n                    items: [\n                        ...((_prev_features = prev.features) === null || _prev_features === void 0 ? void 0 : _prev_features.items) || [],\n                        newFeature\n                    ]\n                }\n            };\n        });\n    };\n    const removeFeature = (id)=>{\n        setContent((prev)=>{\n            var _prev_features_items, _prev_features;\n            return {\n                ...prev,\n                features: {\n                    ...prev.features,\n                    items: ((_prev_features = prev.features) === null || _prev_features === void 0 ? void 0 : (_prev_features_items = _prev_features.items) === null || _prev_features_items === void 0 ? void 0 : _prev_features_items.filter((item)=>item.id !== id)) || []\n                }\n            };\n        });\n    };\n    const updateFeature = (id, key, value)=>{\n        setContent((prev)=>{\n            var _prev_features_items, _prev_features;\n            return {\n                ...prev,\n                features: {\n                    ...prev.features,\n                    items: ((_prev_features = prev.features) === null || _prev_features === void 0 ? void 0 : (_prev_features_items = _prev_features.items) === null || _prev_features_items === void 0 ? void 0 : _prev_features_items.map((item)=>item.id === id ? {\n                            ...item,\n                            [key]: value\n                        } : item)) || []\n                }\n            };\n        });\n    };\n    // Testimonial management functions\n    const addTestimonial = ()=>{\n        const newTestimonial = {\n            id: Date.now().toString(),\n            name: \"Customer Name\",\n            role: \"CEO\",\n            company: \"Company Name\",\n            content: \"Great testimonial content...\",\n            avatar: \"\",\n            rating: 5\n        };\n        setContent((prev)=>{\n            var _prev_testimonials;\n            return {\n                ...prev,\n                testimonials: {\n                    ...prev.testimonials,\n                    items: [\n                        ...((_prev_testimonials = prev.testimonials) === null || _prev_testimonials === void 0 ? void 0 : _prev_testimonials.items) || [],\n                        newTestimonial\n                    ]\n                }\n            };\n        });\n    };\n    const removeTestimonial = (id)=>{\n        setContent((prev)=>{\n            var _prev_testimonials_items, _prev_testimonials;\n            return {\n                ...prev,\n                testimonials: {\n                    ...prev.testimonials,\n                    items: ((_prev_testimonials = prev.testimonials) === null || _prev_testimonials === void 0 ? void 0 : (_prev_testimonials_items = _prev_testimonials.items) === null || _prev_testimonials_items === void 0 ? void 0 : _prev_testimonials_items.filter((item)=>item.id !== id)) || []\n                }\n            };\n        });\n    };\n    const updateTestimonial = (id, key, value)=>{\n        setContent((prev)=>{\n            var _prev_testimonials_items, _prev_testimonials;\n            return {\n                ...prev,\n                testimonials: {\n                    ...prev.testimonials,\n                    items: ((_prev_testimonials = prev.testimonials) === null || _prev_testimonials === void 0 ? void 0 : (_prev_testimonials_items = _prev_testimonials.items) === null || _prev_testimonials_items === void 0 ? void 0 : _prev_testimonials_items.map((item)=>item.id === id ? {\n                            ...item,\n                            [key]: value\n                        } : item)) || []\n                }\n            };\n        });\n    };\n    // FAQ management functions\n    const addFAQ = ()=>{\n        const newFAQ = {\n            id: Date.now().toString(),\n            question: \"New question?\",\n            answer: \"Answer to the question.\"\n        };\n        setContent((prev)=>{\n            var _prev_faq;\n            return {\n                ...prev,\n                faq: {\n                    ...prev.faq,\n                    items: [\n                        ...((_prev_faq = prev.faq) === null || _prev_faq === void 0 ? void 0 : _prev_faq.items) || [],\n                        newFAQ\n                    ]\n                }\n            };\n        });\n    };\n    const removeFAQ = (id)=>{\n        setContent((prev)=>{\n            var _prev_faq_items, _prev_faq;\n            return {\n                ...prev,\n                faq: {\n                    ...prev.faq,\n                    items: ((_prev_faq = prev.faq) === null || _prev_faq === void 0 ? void 0 : (_prev_faq_items = _prev_faq.items) === null || _prev_faq_items === void 0 ? void 0 : _prev_faq_items.filter((item)=>item.id !== id)) || []\n                }\n            };\n        });\n    };\n    const updateFAQ = (id, key, value)=>{\n        setContent((prev)=>{\n            var _prev_faq_items, _prev_faq;\n            return {\n                ...prev,\n                faq: {\n                    ...prev.faq,\n                    items: ((_prev_faq = prev.faq) === null || _prev_faq === void 0 ? void 0 : (_prev_faq_items = _prev_faq.items) === null || _prev_faq_items === void 0 ? void 0 : _prev_faq_items.map((item)=>item.id === id ? {\n                            ...item,\n                            [key]: value\n                        } : item)) || []\n                }\n            };\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center justify-between gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-indigo-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"Landing Page CMS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-1\",\n                                children: \"Manage your landing page content and layout\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: fetchContent,\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>setPreviewMode(!previewMode),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 13\n                                    }, this),\n                                    previewMode ? \"Edit Mode\" : \"Preview\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                onClick: saveContent,\n                                disabled: saving,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(saving ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Save Changes\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 487,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                lineNumber: 479,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.Tabs, {\n                defaultValue: \"hero\",\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsList, {\n                        className: \"grid w-full grid-cols-7\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"hero\",\n                                children: \"Hero\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"features\",\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"pricing\",\n                                children: \"Pricing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"testimonials\",\n                                children: \"Testimonials\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"faq\",\n                                children: \"FAQ\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"cta\",\n                                children: \"CTA\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"seo\",\n                                children: \"SEO\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"hero\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Hero Section\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Configure the main hero section of your landing page\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"heroEnabled\",\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Enable Hero Section\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Show the hero section on your landing page\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                    id: \"heroEnabled\",\n                                                    checked: content.hero.enabled,\n                                                    onCheckedChange: (checked)=>updateContent(\"hero\", \"enabled\", checked)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 15\n                                        }, this),\n                                        content.hero.enabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"heroTitle\",\n                                                                    children: \"Main Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"heroTitle\",\n                                                                    value: content.hero.title,\n                                                                    onChange: (e)=>updateContent(\"hero\", \"title\", e.target.value),\n                                                                    placeholder: \"Your main headline\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 549,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"heroSubtitle\",\n                                                                    children: \"Subtitle\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"heroSubtitle\",\n                                                                    value: content.hero.subtitle,\n                                                                    onChange: (e)=>updateContent(\"hero\", \"subtitle\", e.target.value),\n                                                                    placeholder: \"Supporting headline\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"heroDescription\",\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                            id: \"heroDescription\",\n                                                            value: content.hero.description,\n                                                            onChange: (e)=>updateContent(\"hero\", \"description\", e.target.value),\n                                                            placeholder: \"Describe your product or service\",\n                                                            rows: 3\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"primaryCTAText\",\n                                                                    children: \"Primary Button Text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 580,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"primaryCTAText\",\n                                                                    value: content.hero.primaryCTA.text,\n                                                                    onChange: (e)=>updateContent(\"hero\", \"primaryCTA\", {\n                                                                            ...content.hero.primaryCTA,\n                                                                            text: e.target.value\n                                                                        }),\n                                                                    placeholder: \"Get Started\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"primaryCTALink\",\n                                                                    children: \"Primary Button Link\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 589,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"primaryCTALink\",\n                                                                    value: content.hero.primaryCTA.link,\n                                                                    onChange: (e)=>updateContent(\"hero\", \"primaryCTA\", {\n                                                                            ...content.hero.primaryCTA,\n                                                                            link: e.target.value\n                                                                        }),\n                                                                    placeholder: \"/auth/signup\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 590,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"secondaryCTAText\",\n                                                                    children: \"Secondary Button Text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 601,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"secondaryCTAText\",\n                                                                    value: content.hero.secondaryCTA.text,\n                                                                    onChange: (e)=>updateContent(\"hero\", \"secondaryCTA\", {\n                                                                            ...content.hero.secondaryCTA,\n                                                                            text: e.target.value\n                                                                        }),\n                                                                    placeholder: \"Learn More\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 602,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"secondaryCTALink\",\n                                                                    children: \"Secondary Button Link\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 610,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"secondaryCTALink\",\n                                                                    value: content.hero.secondaryCTA.link,\n                                                                    onChange: (e)=>updateContent(\"hero\", \"secondaryCTA\", {\n                                                                            ...content.hero.secondaryCTA,\n                                                                            link: e.target.value\n                                                                        }),\n                                                                    placeholder: \"/demo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 611,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Background Media\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                            htmlFor: \"heroBackgroundImage\",\n                                                                            children: \"Background Image URL\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 625,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                            id: \"heroBackgroundImage\",\n                                                                            value: content.hero.backgroundImage,\n                                                                            onChange: (e)=>updateContent(\"hero\", \"backgroundImage\", e.target.value),\n                                                                            placeholder: \"https://example.com/hero-bg.jpg\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 626,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 624,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                            htmlFor: \"heroBackgroundVideo\",\n                                                                            children: \"Background Video URL\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 634,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                            id: \"heroBackgroundVideo\",\n                                                                            value: content.hero.backgroundVideo,\n                                                                            onChange: (e)=>updateContent(\"hero\", \"backgroundVideo\", e.target.value),\n                                                                            placeholder: \"https://example.com/hero-video.mp4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 635,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 633,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"features\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Features Section\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 654,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Configure the features showcase section\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"featuresEnabled\",\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Enable Features Section\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Show the features section on your landing page\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                    id: \"featuresEnabled\",\n                                                    checked: ((_content_features = content.features) === null || _content_features === void 0 ? void 0 : _content_features.enabled) || false,\n                                                    onCheckedChange: (checked)=>updateContent(\"features\", \"enabled\", checked)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 15\n                                        }, this),\n                                        ((_content_features1 = content.features) === null || _content_features1 === void 0 ? void 0 : _content_features1.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"featuresTitle\",\n                                                                    children: \"Section Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"featuresTitle\",\n                                                                    value: ((_content_features2 = content.features) === null || _content_features2 === void 0 ? void 0 : _content_features2.title) || \"\",\n                                                                    onChange: (e)=>updateContent(\"features\", \"title\", e.target.value),\n                                                                    placeholder: \"Everything You Need\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 682,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"featuresSubtitle\",\n                                                                    children: \"Section Subtitle\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"featuresSubtitle\",\n                                                                    value: ((_content_features3 = content.features) === null || _content_features3 === void 0 ? void 0 : _content_features3.subtitle) || \"\",\n                                                                    onChange: (e)=>updateContent(\"features\", \"subtitle\", e.target.value),\n                                                                    placeholder: \"Powerful Features\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    className: \"text-base font-medium\",\n                                                                    children: \"Feature Items\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 705,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    onClick: addFeature,\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 707,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Add Feature\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 706,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: (_content_features4 = content.features) === null || _content_features4 === void 0 ? void 0 : (_content_features_items = _content_features4.items) === null || _content_features_items === void 0 ? void 0 : _content_features_items.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                    className: \"p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-start justify-between mb-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: [\n                                                                                        \"Feature \",\n                                                                                        index + 1\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 716,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    variant: \"ghost\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>removeFeature(feature.id),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                        lineNumber: 722,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 717,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 715,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Feature Title\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 727,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                            value: feature.title,\n                                                                                            onChange: (e)=>updateFeature(feature.id, \"title\", e.target.value),\n                                                                                            placeholder: \"Feature title\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 728,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 726,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Icon Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 735,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                            value: feature.icon,\n                                                                                            onChange: (e)=>updateFeature(feature.id, \"icon\", e.target.value),\n                                                                                            className: \"w-full p-2 border border-gray-300 rounded-md\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"users\",\n                                                                                                    children: \"Users\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                                    lineNumber: 741,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"credit-card\",\n                                                                                                    children: \"Credit Card\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                                    lineNumber: 742,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"bar-chart\",\n                                                                                                    children: \"Bar Chart\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                                    lineNumber: 743,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"building\",\n                                                                                                    children: \"Building\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                                    lineNumber: 744,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"shield\",\n                                                                                                    children: \"Shield\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                                    lineNumber: 745,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"globe\",\n                                                                                                    children: \"Globe\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                                    lineNumber: 746,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"zap\",\n                                                                                                    children: \"Zap\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                                    lineNumber: 747,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"file-text\",\n                                                                                                    children: \"File Text\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                                    lineNumber: 748,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 736,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 734,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 725,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2 mt-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                    children: \"Feature Description\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 753,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                                                    value: feature.description,\n                                                                                    onChange: (e)=>updateFeature(feature.id, \"description\", e.target.value),\n                                                                                    placeholder: \"Feature description\",\n                                                                                    rows: 2\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 754,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 752,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, feature.id, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 714,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 712,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                            lineNumber: 652,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 651,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"testimonials\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 776,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Testimonials Section\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 775,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Manage customer testimonials and reviews\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 779,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 774,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"testimonialsEnabled\",\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Enable Testimonials Section\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 786,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Show customer testimonials on your landing page\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                    id: \"testimonialsEnabled\",\n                                                    checked: ((_content_testimonials = content.testimonials) === null || _content_testimonials === void 0 ? void 0 : _content_testimonials.enabled) || false,\n                                                    onCheckedChange: (checked)=>updateContent(\"testimonials\", \"enabled\", checked)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 793,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 15\n                                        }, this),\n                                        ((_content_testimonials1 = content.testimonials) === null || _content_testimonials1 === void 0 ? void 0 : _content_testimonials1.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"testimonialsTitle\",\n                                                                    children: \"Section Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 804,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"testimonialsTitle\",\n                                                                    value: ((_content_testimonials2 = content.testimonials) === null || _content_testimonials2 === void 0 ? void 0 : _content_testimonials2.title) || \"\",\n                                                                    onChange: (e)=>updateContent(\"testimonials\", \"title\", e.target.value),\n                                                                    placeholder: \"What Our Customers Say\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"testimonialsSubtitle\",\n                                                                    children: \"Section Subtitle\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 813,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"testimonialsSubtitle\",\n                                                                    value: ((_content_testimonials3 = content.testimonials) === null || _content_testimonials3 === void 0 ? void 0 : _content_testimonials3.subtitle) || \"\",\n                                                                    onChange: (e)=>updateContent(\"testimonials\", \"subtitle\", e.target.value),\n                                                                    placeholder: \"Trusted by thousands of businesses\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 814,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 812,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 802,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    className: \"text-base font-medium\",\n                                                                    children: \"Testimonials\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 826,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    onClick: addTestimonial,\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 828,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Add Testimonial\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 827,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 825,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: (_content_testimonials4 = content.testimonials) === null || _content_testimonials4 === void 0 ? void 0 : (_content_testimonials_items = _content_testimonials4.items) === null || _content_testimonials_items === void 0 ? void 0 : _content_testimonials_items.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                    className: \"p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-start justify-between mb-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: [\n                                                                                        \"Testimonial \",\n                                                                                        index + 1\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 837,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    variant: \"ghost\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>removeTestimonial(testimonial.id),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                        lineNumber: 843,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 838,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 836,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Customer Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 848,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                            value: testimonial.name,\n                                                                                            onChange: (e)=>updateTestimonial(testimonial.id, \"name\", e.target.value),\n                                                                                            placeholder: \"John Smith\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 849,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 847,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Role\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 856,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                            value: testimonial.role,\n                                                                                            onChange: (e)=>updateTestimonial(testimonial.id, \"role\", e.target.value),\n                                                                                            placeholder: \"CEO\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 857,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 855,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Company\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 864,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                            value: testimonial.company,\n                                                                                            onChange: (e)=>updateTestimonial(testimonial.id, \"company\", e.target.value),\n                                                                                            placeholder: \"TechCorp\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 865,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 863,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 846,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Rating (1-5)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 874,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                            type: \"number\",\n                                                                                            min: \"1\",\n                                                                                            max: \"5\",\n                                                                                            value: testimonial.rating,\n                                                                                            onChange: (e)=>updateTestimonial(testimonial.id, \"rating\", parseInt(e.target.value) || 5)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 875,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 873,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Avatar URL (optional)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 884,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                            value: testimonial.avatar,\n                                                                                            onChange: (e)=>updateTestimonial(testimonial.id, \"avatar\", e.target.value),\n                                                                                            placeholder: \"https://example.com/avatar.jpg\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 885,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 883,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 872,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2 mt-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                    children: \"Testimonial Content\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 893,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                                                    value: testimonial.content,\n                                                                                    onChange: (e)=>updateTestimonial(testimonial.id, \"content\", e.target.value),\n                                                                                    placeholder: \"This platform has transformed our business...\",\n                                                                                    rows: 3\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 894,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 892,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, testimonial.id, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 835,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 833,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 824,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 801,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 783,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                            lineNumber: 773,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 772,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"faq\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"FAQ Section\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 915,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Manage frequently asked questions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 916,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 914,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"faqEnabled\",\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Enable FAQ Section\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 923,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Show FAQ section on your landing page\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 926,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 922,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                    id: \"faqEnabled\",\n                                                    checked: ((_content_faq = content.faq) === null || _content_faq === void 0 ? void 0 : _content_faq.enabled) || false,\n                                                    onCheckedChange: (checked)=>updateContent(\"faq\", \"enabled\", checked)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 930,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 921,\n                                            columnNumber: 15\n                                        }, this),\n                                        ((_content_faq1 = content.faq) === null || _content_faq1 === void 0 ? void 0 : _content_faq1.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"faqTitle\",\n                                                                    children: \"Section Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 941,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"faqTitle\",\n                                                                    value: ((_content_faq2 = content.faq) === null || _content_faq2 === void 0 ? void 0 : _content_faq2.title) || \"\",\n                                                                    onChange: (e)=>updateContent(\"faq\", \"title\", e.target.value),\n                                                                    placeholder: \"Frequently Asked Questions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 942,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 940,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"faqSubtitle\",\n                                                                    children: \"Section Subtitle\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 950,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"faqSubtitle\",\n                                                                    value: ((_content_faq3 = content.faq) === null || _content_faq3 === void 0 ? void 0 : _content_faq3.subtitle) || \"\",\n                                                                    onChange: (e)=>updateContent(\"faq\", \"subtitle\", e.target.value),\n                                                                    placeholder: \"Everything you need to know\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 951,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 949,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 939,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    className: \"text-base font-medium\",\n                                                                    children: \"FAQ Items\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 963,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    onClick: addFAQ,\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 965,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Add FAQ\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 964,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 962,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: (_content_faq4 = content.faq) === null || _content_faq4 === void 0 ? void 0 : (_content_faq_items = _content_faq4.items) === null || _content_faq_items === void 0 ? void 0 : _content_faq_items.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                    className: \"p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-start justify-between mb-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: [\n                                                                                        \"FAQ \",\n                                                                                        index + 1\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 974,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    variant: \"ghost\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>removeFAQ(faq.id),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                        lineNumber: 980,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 975,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 973,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Question\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 985,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                            value: faq.question,\n                                                                                            onChange: (e)=>updateFAQ(faq.id, \"question\", e.target.value),\n                                                                                            placeholder: \"What is your question?\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 986,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 984,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Answer\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 993,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                                                            value: faq.answer,\n                                                                                            onChange: (e)=>updateFAQ(faq.id, \"answer\", e.target.value),\n                                                                                            placeholder: \"Provide a detailed answer...\",\n                                                                                            rows: 3\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 994,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 992,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 983,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, faq.id, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 972,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 970,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 961,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 938,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 920,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                            lineNumber: 913,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 912,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"cta\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Call-to-Action Section\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1016,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Configure the final call-to-action section\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1017,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 1015,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"ctaEnabled\",\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Enable CTA Section\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1024,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Show the call-to-action section on your landing page\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1027,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1023,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                    id: \"ctaEnabled\",\n                                                    checked: ((_content_cta = content.cta) === null || _content_cta === void 0 ? void 0 : _content_cta.enabled) || false,\n                                                    onCheckedChange: (checked)=>updateContent(\"cta\", \"enabled\", checked)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1031,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1022,\n                                            columnNumber: 15\n                                        }, this),\n                                        ((_content_cta1 = content.cta) === null || _content_cta1 === void 0 ? void 0 : _content_cta1.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"ctaTitle\",\n                                                                    children: \"CTA Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 1042,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"ctaTitle\",\n                                                                    value: ((_content_cta2 = content.cta) === null || _content_cta2 === void 0 ? void 0 : _content_cta2.title) || \"\",\n                                                                    onChange: (e)=>updateContent(\"cta\", \"title\", e.target.value),\n                                                                    placeholder: \"Ready to Get Started?\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 1043,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1041,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"ctaButtonText\",\n                                                                    children: \"Button Text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 1051,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"ctaButtonText\",\n                                                                    value: ((_content_cta3 = content.cta) === null || _content_cta3 === void 0 ? void 0 : _content_cta3.buttonText) || \"\",\n                                                                    onChange: (e)=>updateContent(\"cta\", \"buttonText\", e.target.value),\n                                                                    placeholder: \"Start Your Free Trial\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 1052,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1050,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1040,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"ctaDescription\",\n                                                            children: \"CTA Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1062,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                            id: \"ctaDescription\",\n                                                            value: ((_content_cta4 = content.cta) === null || _content_cta4 === void 0 ? void 0 : _content_cta4.description) || \"\",\n                                                            onChange: (e)=>updateContent(\"cta\", \"description\", e.target.value),\n                                                            placeholder: \"Join thousands of businesses already using our platform...\",\n                                                            rows: 3\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1063,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1061,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"ctaButtonLink\",\n                                                                    children: \"Button Link\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 1074,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"ctaButtonLink\",\n                                                                    value: ((_content_cta5 = content.cta) === null || _content_cta5 === void 0 ? void 0 : _content_cta5.buttonLink) || \"\",\n                                                                    onChange: (e)=>updateContent(\"cta\", \"buttonLink\", e.target.value),\n                                                                    placeholder: \"/auth/signup\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 1075,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1073,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"ctaBackgroundImage\",\n                                                                    children: \"Background Image URL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 1083,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"ctaBackgroundImage\",\n                                                                    value: ((_content_cta6 = content.cta) === null || _content_cta6 === void 0 ? void 0 : _content_cta6.backgroundImage) || \"\",\n                                                                    onChange: (e)=>updateContent(\"cta\", \"backgroundImage\", e.target.value),\n                                                                    placeholder: \"https://example.com/cta-bg.jpg\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 1084,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1082,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1072,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1039,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                            lineNumber: 1014,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 1013,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"seo\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"SEO Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Configure SEO meta tags and social sharing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1103,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 1101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"seoTitle\",\n                                                            children: \"Page Title\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1110,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            id: \"seoTitle\",\n                                                            value: ((_content_seo = content.seo) === null || _content_seo === void 0 ? void 0 : _content_seo.title) || \"\",\n                                                            onChange: (e)=>updateContent(\"seo\", \"title\", e.target.value),\n                                                            placeholder: \"SaaS Platform - Build Your Business\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1111,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"Recommended: 50-60 characters\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1117,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1109,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"seoKeywords\",\n                                                            children: \"Keywords\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1120,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            id: \"seoKeywords\",\n                                                            value: ((_content_seo1 = content.seo) === null || _content_seo1 === void 0 ? void 0 : _content_seo1.keywords) || \"\",\n                                                            onChange: (e)=>updateContent(\"seo\", \"keywords\", e.target.value),\n                                                            placeholder: \"saas, platform, business, management\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1121,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"Comma-separated keywords\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1127,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1119,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1108,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                    htmlFor: \"seoDescription\",\n                                                    children: \"Meta Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1132,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                    id: \"seoDescription\",\n                                                    value: ((_content_seo2 = content.seo) === null || _content_seo2 === void 0 ? void 0 : _content_seo2.description) || \"\",\n                                                    onChange: (e)=>updateContent(\"seo\", \"description\", e.target.value),\n                                                    placeholder: \"The complete SaaS platform for modern businesses...\",\n                                                    rows: 3\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1133,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Recommended: 150-160 characters\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1140,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1131,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                    htmlFor: \"seoOgImage\",\n                                                    children: \"Open Graph Image URL\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1144,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                    id: \"seoOgImage\",\n                                                    value: ((_content_seo3 = content.seo) === null || _content_seo3 === void 0 ? void 0 : _content_seo3.ogImage) || \"\",\n                                                    onChange: (e)=>updateContent(\"seo\", \"ogImage\", e.target.value),\n                                                    placeholder: \"https://example.com/og-image.jpg\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1145,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Recommended: 1200x630px\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1151,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1143,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 1107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                            lineNumber: 1100,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 1099,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                lineNumber: 504,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n        lineNumber: 477,\n        columnNumber: 5\n    }, this);\n}\n_s(CMSPage, \"PXqsaU0wp/5yXV80WFnZdgCyYW0=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = CMSPage;\nvar _c;\n$RefreshReg$(_c, \"CMSPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9zdXBlci1hZG1pbi9jbXMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ0M7QUFDRjtBQUNzRDtBQUNqRDtBQUNGO0FBQ0E7QUFDTTtBQUNKO0FBQ2dDO0FBbUIxRDtBQUNrQjtBQWlIeEIsU0FBUzRCO1FBcUpsQkMsZUE0WHFCQyxtQkFLWkEsb0JBT2dCQSxvQkFTQUEsb0JBa0JSQSx5QkFBQUEsb0JBa0ZJQSx1QkFLWkEsd0JBT2dCQSx3QkFTQUEsd0JBa0JSQSw2QkFBQUEsd0JBa0dJQSxjQUtaQSxlQU9nQkEsZUFTQUEsZUFrQlJBLG9CQUFBQSxlQThESUEsY0FLWkEsZUFPZ0JBLGVBU0FBLGVBV0ZBLGVBWUVBLGVBU0FBLGVBMkJKQSxjQVVBQSxlQVlGQSxlQVlBQTs7SUF6K0J2QixNQUFNLEVBQUVDLE1BQU1GLE9BQU8sRUFBRUcsTUFBTSxFQUFFLEdBQUc5QiwyREFBVUE7SUFDNUMsTUFBTSxDQUFDNEIsU0FBU0csV0FBVyxHQUFHakMsK0NBQVFBLENBQWE7UUFDakRrQyxNQUFNO1lBQ0pDLFNBQVM7WUFDVEMsT0FBTztZQUNQQyxVQUFVO1lBQ1ZDLGFBQWE7WUFDYkMsWUFBWTtnQkFDVkMsTUFBTTtnQkFDTkMsTUFBTTtZQUNSO1lBQ0FDLGNBQWM7Z0JBQ1pGLE1BQU07Z0JBQ05DLE1BQU07WUFDUjtZQUNBRSxpQkFBaUI7WUFDakJDLGlCQUFpQjtRQUNuQjtRQUNBQyxVQUFVO1lBQ1JWLFNBQVM7WUFDVEMsT0FBTztZQUNQQyxVQUFVO1lBQ1ZTLE9BQU87Z0JBQ0w7b0JBQ0VDLElBQUk7b0JBQ0pYLE9BQU87b0JBQ1BFLGFBQWE7b0JBQ2JVLE1BQU07b0JBQ05DLE9BQU87Z0JBQ1Q7Z0JBQ0E7b0JBQ0VGLElBQUk7b0JBQ0pYLE9BQU87b0JBQ1BFLGFBQWE7b0JBQ2JVLE1BQU07b0JBQ05DLE9BQU87Z0JBQ1Q7Z0JBQ0E7b0JBQ0VGLElBQUk7b0JBQ0pYLE9BQU87b0JBQ1BFLGFBQWE7b0JBQ2JVLE1BQU07b0JBQ05DLE9BQU87Z0JBQ1Q7YUFDRDtRQUNIO1FBQ0FDLFNBQVM7WUFDUGYsU0FBUztZQUNUQyxPQUFPO1lBQ1BDLFVBQVU7WUFDVmMsa0JBQWtCO1lBQ2xCQyxlQUFlO1FBQ2pCO1FBQ0FDLGNBQWM7WUFDWmxCLFNBQVM7WUFDVEMsT0FBTztZQUNQQyxVQUFVO1lBQ1ZTLE9BQU87Z0JBQ0w7b0JBQ0VDLElBQUk7b0JBQ0pPLE1BQU07b0JBQ05DLE1BQU07b0JBQ05DLFNBQVM7b0JBQ1QxQixTQUFTO29CQUNUMkIsUUFBUTtvQkFDUkMsUUFBUTtnQkFDVjthQUNEO1FBQ0g7UUFDQUMsS0FBSztZQUNIeEIsU0FBUztZQUNUQyxPQUFPO1lBQ1BDLFVBQVU7WUFDVlMsT0FBTztnQkFDTDtvQkFDRUMsSUFBSTtvQkFDSmEsVUFBVTtvQkFDVkMsUUFBUTtnQkFDVjtnQkFDQTtvQkFDRWQsSUFBSTtvQkFDSmEsVUFBVTtvQkFDVkMsUUFBUTtnQkFDVjthQUNEO1FBQ0g7UUFDQUMsS0FBSztZQUNIM0IsU0FBUztZQUNUQyxPQUFPO1lBQ1BFLGFBQWE7WUFDYnlCLFlBQVk7WUFDWkMsWUFBWTtZQUNackIsaUJBQWlCO1FBQ25CO1FBQ0FzQixRQUFRO1lBQ045QixTQUFTO1lBQ1QrQixvQkFBb0I7WUFDcEJDLE9BQU87Z0JBQ0w7b0JBQ0VwQixJQUFJO29CQUNKWCxPQUFPO29CQUNQVSxPQUFPO3dCQUNMOzRCQUFFQyxJQUFJOzRCQUFLUCxNQUFNOzRCQUFZQyxNQUFNO3dCQUFZO3dCQUMvQzs0QkFBRU0sSUFBSTs0QkFBS1AsTUFBTTs0QkFBV0MsTUFBTTt3QkFBVzt3QkFDN0M7NEJBQUVNLElBQUk7NEJBQUtQLE1BQU07NEJBQVlDLE1BQU07d0JBQVk7cUJBQ2hEO2dCQUNIO2dCQUNBO29CQUNFTSxJQUFJO29CQUNKWCxPQUFPO29CQUNQVSxPQUFPO3dCQUNMOzRCQUFFQyxJQUFJOzRCQUFLUCxNQUFNOzRCQUFTQyxNQUFNO3dCQUFTO3dCQUN6Qzs0QkFBRU0sSUFBSTs0QkFBS1AsTUFBTTs0QkFBUUMsTUFBTTt3QkFBUTt3QkFDdkM7NEJBQUVNLElBQUk7NEJBQUtQLE1BQU07NEJBQVdDLE1BQU07d0JBQVc7cUJBQzlDO2dCQUNIO2FBQ0Q7WUFDRDJCLGFBQWE7Z0JBQ1hDLFNBQVM7Z0JBQ1RDLFVBQVU7Z0JBQ1ZDLFVBQVU7Z0JBQ1ZDLFdBQVc7WUFDYjtZQUNBQyxlQUFlO1FBQ2pCO1FBQ0FDLEtBQUs7WUFDSHRDLE9BQU87WUFDUEUsYUFBYTtZQUNicUMsVUFBVTtZQUNWQyxTQUFTO1FBQ1g7SUFDRjtJQUNBLE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHOUUsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDK0UsUUFBUUMsVUFBVSxHQUFHaEYsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDaUYsYUFBYUMsZUFBZSxHQUFHbEYsK0NBQVFBLENBQUM7SUFFL0MsSUFBSWdDLFdBQVcsV0FBVztRQUN4QixxQkFDRSw4REFBQ21EO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOzs7Ozs7Ozs7OztJQUdyQjtJQUVBLElBQUlwRCxXQUFXLG1CQUFtQjtRQUNoQzdCLHlEQUFRQSxDQUFDO0lBQ1g7SUFFQSxJQUFJMEIsQ0FBQUEsb0JBQUFBLCtCQUFBQSxnQkFBQUEsUUFBU3dELElBQUksY0FBYnhELG9DQUFBQSxjQUFlMEIsSUFBSSxNQUFLLGVBQWU7UUFDekNwRCx5REFBUUEsQ0FBQztJQUNYO0lBRUEsTUFBTW1GLGVBQWU7UUFDbkIsSUFBSTtZQUNGUixXQUFXO1lBQ1gsTUFBTVMsV0FBVyxNQUFNQyxNQUFNO1lBQzdCLE1BQU16RCxPQUFPLE1BQU13RCxTQUFTRSxJQUFJO1lBRWhDLElBQUkxRCxLQUFLMkQsT0FBTyxFQUFFO2dCQUNoQnpELFdBQVc7b0JBQUUsR0FBR0gsT0FBTztvQkFBRSxHQUFHQyxLQUFLRCxPQUFPO2dCQUFDO1lBQzNDO1FBQ0YsRUFBRSxPQUFPNkQsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsK0JBQStCQTtZQUM3Q2hFLG1EQUFLQSxDQUFDZ0UsS0FBSyxDQUFDO1FBQ2QsU0FBVTtZQUNSYixXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU1lLGNBQWM7UUFDbEIsSUFBSTtZQUNGYixVQUFVO1lBQ1YsTUFBTU8sV0FBVyxNQUFNQyxNQUFNLHdCQUF3QjtnQkFDbkRNLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsYUFBYTtnQkFDYkMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDckU7WUFDdkI7WUFFQSxNQUFNQyxPQUFPLE1BQU13RCxTQUFTRSxJQUFJO1lBRWhDLElBQUkxRCxLQUFLMkQsT0FBTyxFQUFFO2dCQUNoQi9ELG1EQUFLQSxDQUFDK0QsT0FBTyxDQUFDO1lBQ2hCLE9BQU87Z0JBQ0wvRCxtREFBS0EsQ0FBQ2dFLEtBQUssQ0FBQzVELEtBQUs0RCxLQUFLLElBQUk7WUFDNUI7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkE7WUFDM0NoRSxtREFBS0EsQ0FBQ2dFLEtBQUssQ0FBQztRQUNkLFNBQVU7WUFDUlgsVUFBVTtRQUNaO0lBQ0Y7SUFFQS9FLGdEQUFTQSxDQUFDO1FBQ1JxRjtJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU1jLGdCQUFnQixDQUFDQyxTQUEyQkMsS0FBYUM7UUFDN0R0RSxXQUFXdUUsQ0FBQUEsT0FBUztnQkFDbEIsR0FBR0EsSUFBSTtnQkFDUCxDQUFDSCxRQUFRLEVBQUU7b0JBQ1QsR0FBR0csSUFBSSxDQUFDSCxRQUFRO29CQUNoQixDQUFDQyxJQUFJLEVBQUVDO2dCQUNUO1lBQ0Y7SUFDRjtJQUVBLCtCQUErQjtJQUMvQixNQUFNRSxhQUFhO1FBQ2pCLE1BQU1DLGFBQWE7WUFDakIzRCxJQUFJNEQsS0FBS0MsR0FBRyxHQUFHQyxRQUFRO1lBQ3ZCekUsT0FBTztZQUNQRSxhQUFhO1lBQ2JVLE1BQU07WUFDTkMsT0FBTztRQUNUO1FBQ0FoQixXQUFXdUUsQ0FBQUE7Z0JBSUtBO21CQUpJO2dCQUNsQixHQUFHQSxJQUFJO2dCQUNQM0QsVUFBVTtvQkFDUixHQUFHMkQsS0FBSzNELFFBQVE7b0JBQ2hCQyxPQUFPOzJCQUFLMEQsRUFBQUEsaUJBQUFBLEtBQUszRCxRQUFRLGNBQWIyRCxxQ0FBQUEsZUFBZTFELEtBQUssS0FBSSxFQUFFO3dCQUFHNEQ7cUJBQVc7Z0JBQ3REO1lBQ0Y7O0lBQ0Y7SUFFQSxNQUFNSSxnQkFBZ0IsQ0FBQy9EO1FBQ3JCZCxXQUFXdUUsQ0FBQUE7Z0JBSUFBLHNCQUFBQTttQkFKUztnQkFDbEIsR0FBR0EsSUFBSTtnQkFDUDNELFVBQVU7b0JBQ1IsR0FBRzJELEtBQUszRCxRQUFRO29CQUNoQkMsT0FBTzBELEVBQUFBLGlCQUFBQSxLQUFLM0QsUUFBUSxjQUFiMkQsc0NBQUFBLHVCQUFBQSxlQUFlMUQsS0FBSyxjQUFwQjBELDJDQUFBQSxxQkFBc0JPLE1BQU0sQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS2pFLEVBQUUsS0FBS0EsUUFBTyxFQUFFO2dCQUNuRTtZQUNGOztJQUNGO0lBRUEsTUFBTWtFLGdCQUFnQixDQUFDbEUsSUFBWXVELEtBQWFDO1FBQzlDdEUsV0FBV3VFLENBQUFBO2dCQUlBQSxzQkFBQUE7bUJBSlM7Z0JBQ2xCLEdBQUdBLElBQUk7Z0JBQ1AzRCxVQUFVO29CQUNSLEdBQUcyRCxLQUFLM0QsUUFBUTtvQkFDaEJDLE9BQU8wRCxFQUFBQSxpQkFBQUEsS0FBSzNELFFBQVEsY0FBYjJELHNDQUFBQSx1QkFBQUEsZUFBZTFELEtBQUssY0FBcEIwRCwyQ0FBQUEscUJBQXNCVSxHQUFHLENBQUNGLENBQUFBLE9BQy9CQSxLQUFLakUsRUFBRSxLQUFLQSxLQUFLOzRCQUFFLEdBQUdpRSxJQUFJOzRCQUFFLENBQUNWLElBQUksRUFBRUM7d0JBQU0sSUFBSVMsVUFDMUMsRUFBRTtnQkFDVDtZQUNGOztJQUNGO0lBRUEsbUNBQW1DO0lBQ25DLE1BQU1HLGlCQUFpQjtRQUNyQixNQUFNQyxpQkFBaUI7WUFDckJyRSxJQUFJNEQsS0FBS0MsR0FBRyxHQUFHQyxRQUFRO1lBQ3ZCdkQsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFNBQVM7WUFDVDFCLFNBQVM7WUFDVDJCLFFBQVE7WUFDUkMsUUFBUTtRQUNWO1FBQ0F6QixXQUFXdUUsQ0FBQUE7Z0JBSUtBO21CQUpJO2dCQUNsQixHQUFHQSxJQUFJO2dCQUNQbkQsY0FBYztvQkFDWixHQUFHbUQsS0FBS25ELFlBQVk7b0JBQ3BCUCxPQUFPOzJCQUFLMEQsRUFBQUEscUJBQUFBLEtBQUtuRCxZQUFZLGNBQWpCbUQseUNBQUFBLG1CQUFtQjFELEtBQUssS0FBSSxFQUFFO3dCQUFHc0U7cUJBQWU7Z0JBQzlEO1lBQ0Y7O0lBQ0Y7SUFFQSxNQUFNQyxvQkFBb0IsQ0FBQ3RFO1FBQ3pCZCxXQUFXdUUsQ0FBQUE7Z0JBSUFBLDBCQUFBQTttQkFKUztnQkFDbEIsR0FBR0EsSUFBSTtnQkFDUG5ELGNBQWM7b0JBQ1osR0FBR21ELEtBQUtuRCxZQUFZO29CQUNwQlAsT0FBTzBELEVBQUFBLHFCQUFBQSxLQUFLbkQsWUFBWSxjQUFqQm1ELDBDQUFBQSwyQkFBQUEsbUJBQW1CMUQsS0FBSyxjQUF4QjBELCtDQUFBQSx5QkFBMEJPLE1BQU0sQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS2pFLEVBQUUsS0FBS0EsUUFBTyxFQUFFO2dCQUN2RTtZQUNGOztJQUNGO0lBRUEsTUFBTXVFLG9CQUFvQixDQUFDdkUsSUFBWXVELEtBQWFDO1FBQ2xEdEUsV0FBV3VFLENBQUFBO2dCQUlBQSwwQkFBQUE7bUJBSlM7Z0JBQ2xCLEdBQUdBLElBQUk7Z0JBQ1BuRCxjQUFjO29CQUNaLEdBQUdtRCxLQUFLbkQsWUFBWTtvQkFDcEJQLE9BQU8wRCxFQUFBQSxxQkFBQUEsS0FBS25ELFlBQVksY0FBakJtRCwwQ0FBQUEsMkJBQUFBLG1CQUFtQjFELEtBQUssY0FBeEIwRCwrQ0FBQUEseUJBQTBCVSxHQUFHLENBQUNGLENBQUFBLE9BQ25DQSxLQUFLakUsRUFBRSxLQUFLQSxLQUFLOzRCQUFFLEdBQUdpRSxJQUFJOzRCQUFFLENBQUNWLElBQUksRUFBRUM7d0JBQU0sSUFBSVMsVUFDMUMsRUFBRTtnQkFDVDtZQUNGOztJQUNGO0lBRUEsMkJBQTJCO0lBQzNCLE1BQU1PLFNBQVM7UUFDYixNQUFNQyxTQUFTO1lBQ2J6RSxJQUFJNEQsS0FBS0MsR0FBRyxHQUFHQyxRQUFRO1lBQ3ZCakQsVUFBVTtZQUNWQyxRQUFRO1FBQ1Y7UUFDQTVCLFdBQVd1RSxDQUFBQTtnQkFJS0E7bUJBSkk7Z0JBQ2xCLEdBQUdBLElBQUk7Z0JBQ1A3QyxLQUFLO29CQUNILEdBQUc2QyxLQUFLN0MsR0FBRztvQkFDWGIsT0FBTzsyQkFBSzBELEVBQUFBLFlBQUFBLEtBQUs3QyxHQUFHLGNBQVI2QyxnQ0FBQUEsVUFBVTFELEtBQUssS0FBSSxFQUFFO3dCQUFHMEU7cUJBQU87Z0JBQzdDO1lBQ0Y7O0lBQ0Y7SUFFQSxNQUFNQyxZQUFZLENBQUMxRTtRQUNqQmQsV0FBV3VFLENBQUFBO2dCQUlBQSxpQkFBQUE7bUJBSlM7Z0JBQ2xCLEdBQUdBLElBQUk7Z0JBQ1A3QyxLQUFLO29CQUNILEdBQUc2QyxLQUFLN0MsR0FBRztvQkFDWGIsT0FBTzBELEVBQUFBLFlBQUFBLEtBQUs3QyxHQUFHLGNBQVI2QyxpQ0FBQUEsa0JBQUFBLFVBQVUxRCxLQUFLLGNBQWYwRCxzQ0FBQUEsZ0JBQWlCTyxNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtqRSxFQUFFLEtBQUtBLFFBQU8sRUFBRTtnQkFDOUQ7WUFDRjs7SUFDRjtJQUVBLE1BQU0yRSxZQUFZLENBQUMzRSxJQUFZdUQsS0FBYUM7UUFDMUN0RSxXQUFXdUUsQ0FBQUE7Z0JBSUFBLGlCQUFBQTttQkFKUztnQkFDbEIsR0FBR0EsSUFBSTtnQkFDUDdDLEtBQUs7b0JBQ0gsR0FBRzZDLEtBQUs3QyxHQUFHO29CQUNYYixPQUFPMEQsRUFBQUEsWUFBQUEsS0FBSzdDLEdBQUcsY0FBUjZDLGlDQUFBQSxrQkFBQUEsVUFBVTFELEtBQUssY0FBZjBELHNDQUFBQSxnQkFBaUJVLEdBQUcsQ0FBQ0YsQ0FBQUEsT0FDMUJBLEtBQUtqRSxFQUFFLEtBQUtBLEtBQUs7NEJBQUUsR0FBR2lFLElBQUk7NEJBQUUsQ0FBQ1YsSUFBSSxFQUFFQzt3QkFBTSxJQUFJUyxVQUMxQyxFQUFFO2dCQUNUO1lBQ0Y7O0lBQ0Y7SUFFQSxxQkFDRSw4REFBQzdCO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEOzswQ0FDQyw4REFBQ0E7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDbEUscUlBQU9BO3dDQUFDa0UsV0FBVTs7Ozs7O2tEQUNuQiw4REFBQ3VDO3dDQUFHdkMsV0FBVTtrREFBbUM7Ozs7Ozs7Ozs7OzswQ0FFbkQsOERBQUN3QztnQ0FBRXhDLFdBQVU7MENBQXFCOzs7Ozs7Ozs7Ozs7a0NBRXBDLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUMzRSx5REFBTUE7Z0NBQUNvSCxTQUFRO2dDQUFVQyxTQUFTeEM7Z0NBQWN5QyxVQUFVbEQ7O2tEQUN6RCw4REFBQ3pELHFJQUFTQTt3Q0FBQ2dFLFdBQVcsZ0JBQThDLE9BQTlCUCxVQUFVLGlCQUFpQjs7Ozs7O29DQUFROzs7Ozs7OzBDQUczRSw4REFBQ3BFLHlEQUFNQTtnQ0FBQ29ILFNBQVE7Z0NBQVVDLFNBQVMsSUFBTTVDLGVBQWUsQ0FBQ0Q7O2tEQUN2RCw4REFBQzVELHFJQUFHQTt3Q0FBQytELFdBQVU7Ozs7OztvQ0FDZEgsY0FBYyxjQUFjOzs7Ozs7OzBDQUUvQiw4REFBQ3hFLHlEQUFNQTtnQ0FBQ3FILFNBQVNqQztnQ0FBYWtDLFVBQVVoRDs7a0RBQ3RDLDhEQUFDNUQscUlBQUlBO3dDQUFDaUUsV0FBVyxnQkFBNkMsT0FBN0JMLFNBQVMsaUJBQWlCOzs7Ozs7b0NBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT3pFLDhEQUFDakUsc0RBQUlBO2dCQUFDa0gsY0FBYTtnQkFBTzVDLFdBQVU7O2tDQUNsQyw4REFBQ3BFLDBEQUFRQTt3QkFBQ29FLFdBQVU7OzBDQUNsQiw4REFBQ25FLDZEQUFXQTtnQ0FBQ3NGLE9BQU07MENBQU87Ozs7OzswQ0FDMUIsOERBQUN0Riw2REFBV0E7Z0NBQUNzRixPQUFNOzBDQUFXOzs7Ozs7MENBQzlCLDhEQUFDdEYsNkRBQVdBO2dDQUFDc0YsT0FBTTswQ0FBVTs7Ozs7OzBDQUM3Qiw4REFBQ3RGLDZEQUFXQTtnQ0FBQ3NGLE9BQU07MENBQWU7Ozs7OzswQ0FDbEMsOERBQUN0Riw2REFBV0E7Z0NBQUNzRixPQUFNOzBDQUFNOzs7Ozs7MENBQ3pCLDhEQUFDdEYsNkRBQVdBO2dDQUFDc0YsT0FBTTswQ0FBTTs7Ozs7OzBDQUN6Qiw4REFBQ3RGLDZEQUFXQTtnQ0FBQ3NGLE9BQU07MENBQU07Ozs7Ozs7Ozs7OztrQ0FJM0IsOERBQUN4Riw2REFBV0E7d0JBQUN3RixPQUFNO2tDQUNqQiw0RUFBQ25HLHFEQUFJQTs7OENBQ0gsOERBQUNHLDJEQUFVQTs7c0RBQ1QsOERBQUNDLDBEQUFTQTs0Q0FBQzRFLFdBQVU7OzhEQUNuQiw4REFBQzVELHFJQUFNQTtvREFBQzRELFdBQVU7Ozs7OztnREFBaUI7Ozs7Ozs7c0RBR3JDLDhEQUFDOUUsZ0VBQWVBO3NEQUFDOzs7Ozs7Ozs7Ozs7OENBSW5CLDhEQUFDRCw0REFBV0E7b0NBQUMrRSxXQUFVOztzREFDckIsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7O3NFQUNDLDhEQUFDeEUsdURBQUtBOzREQUFDc0gsU0FBUTs0REFBYzdDLFdBQVU7c0VBQXdCOzs7Ozs7c0VBRy9ELDhEQUFDd0M7NERBQUV4QyxXQUFVO3NFQUF3Qjs7Ozs7Ozs7Ozs7OzhEQUl2Qyw4REFBQ3ZFLHlEQUFNQTtvREFDTGtDLElBQUc7b0RBQ0htRixTQUFTcEcsUUFBUUksSUFBSSxDQUFDQyxPQUFPO29EQUM3QmdHLGlCQUFpQixDQUFDRCxVQUFZOUIsY0FBYyxRQUFRLFdBQVc4Qjs7Ozs7Ozs7Ozs7O3dDQUlsRXBHLFFBQVFJLElBQUksQ0FBQ0MsT0FBTyxrQkFDbkIsOERBQUNnRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDekUsdURBQUtBO29FQUFDc0gsU0FBUTs4RUFBWTs7Ozs7OzhFQUMzQiw4REFBQ3ZILHVEQUFLQTtvRUFDSnFDLElBQUc7b0VBQ0h3RCxPQUFPekUsUUFBUUksSUFBSSxDQUFDRSxLQUFLO29FQUN6QmdHLFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsUUFBUSxTQUFTaUMsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSztvRUFDOURnQyxhQUFZOzs7Ozs7Ozs7Ozs7c0VBR2hCLDhEQUFDcEQ7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDekUsdURBQUtBO29FQUFDc0gsU0FBUTs4RUFBZTs7Ozs7OzhFQUM5Qiw4REFBQ3ZILHVEQUFLQTtvRUFDSnFDLElBQUc7b0VBQ0h3RCxPQUFPekUsUUFBUUksSUFBSSxDQUFDRyxRQUFRO29FQUM1QitGLFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsUUFBUSxZQUFZaUMsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSztvRUFDakVnQyxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBS2xCLDhEQUFDcEQ7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDekUsdURBQUtBOzREQUFDc0gsU0FBUTtzRUFBa0I7Ozs7OztzRUFDakMsOERBQUNySCw2REFBUUE7NERBQ1BtQyxJQUFHOzREQUNId0QsT0FBT3pFLFFBQVFJLElBQUksQ0FBQ0ksV0FBVzs0REFDL0I4RixVQUFVLENBQUNDLElBQU1qQyxjQUFjLFFBQVEsZUFBZWlDLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7NERBQ3BFZ0MsYUFBWTs0REFDWkMsTUFBTTs7Ozs7Ozs7Ozs7OzhEQUlWLDhEQUFDckQ7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUN6RSx1REFBS0E7b0VBQUNzSCxTQUFROzhFQUFpQjs7Ozs7OzhFQUNoQyw4REFBQ3ZILHVEQUFLQTtvRUFDSnFDLElBQUc7b0VBQ0h3RCxPQUFPekUsUUFBUUksSUFBSSxDQUFDSyxVQUFVLENBQUNDLElBQUk7b0VBQ25DNEYsVUFBVSxDQUFDQyxJQUFNakMsY0FBYyxRQUFRLGNBQWM7NEVBQUUsR0FBR3RFLFFBQVFJLElBQUksQ0FBQ0ssVUFBVTs0RUFBRUMsTUFBTTZGLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7d0VBQUM7b0VBQ3hHZ0MsYUFBWTs7Ozs7Ozs7Ozs7O3NFQUdoQiw4REFBQ3BEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ3pFLHVEQUFLQTtvRUFBQ3NILFNBQVE7OEVBQWlCOzs7Ozs7OEVBQ2hDLDhEQUFDdkgsdURBQUtBO29FQUNKcUMsSUFBRztvRUFDSHdELE9BQU96RSxRQUFRSSxJQUFJLENBQUNLLFVBQVUsQ0FBQ0UsSUFBSTtvRUFDbkMyRixVQUFVLENBQUNDLElBQU1qQyxjQUFjLFFBQVEsY0FBYzs0RUFBRSxHQUFHdEUsUUFBUUksSUFBSSxDQUFDSyxVQUFVOzRFQUFFRSxNQUFNNEYsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSzt3RUFBQztvRUFDeEdnQyxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBS2xCLDhEQUFDcEQ7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUN6RSx1REFBS0E7b0VBQUNzSCxTQUFROzhFQUFtQjs7Ozs7OzhFQUNsQyw4REFBQ3ZILHVEQUFLQTtvRUFDSnFDLElBQUc7b0VBQ0h3RCxPQUFPekUsUUFBUUksSUFBSSxDQUFDUSxZQUFZLENBQUNGLElBQUk7b0VBQ3JDNEYsVUFBVSxDQUFDQyxJQUFNakMsY0FBYyxRQUFRLGdCQUFnQjs0RUFBRSxHQUFHdEUsUUFBUUksSUFBSSxDQUFDUSxZQUFZOzRFQUFFRixNQUFNNkYsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSzt3RUFBQztvRUFDNUdnQyxhQUFZOzs7Ozs7Ozs7Ozs7c0VBR2hCLDhEQUFDcEQ7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDekUsdURBQUtBO29FQUFDc0gsU0FBUTs4RUFBbUI7Ozs7Ozs4RUFDbEMsOERBQUN2SCx1REFBS0E7b0VBQ0pxQyxJQUFHO29FQUNId0QsT0FBT3pFLFFBQVFJLElBQUksQ0FBQ1EsWUFBWSxDQUFDRCxJQUFJO29FQUNyQzJGLFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsUUFBUSxnQkFBZ0I7NEVBQUUsR0FBR3RFLFFBQVFJLElBQUksQ0FBQ1EsWUFBWTs0RUFBRUQsTUFBTTRGLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7d0VBQUM7b0VBQzVHZ0MsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQU1sQiw4REFBQ3BEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3pFLHVEQUFLQTs0REFBQ3lFLFdBQVU7c0VBQXdCOzs7Ozs7c0VBQ3pDLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ3pFLHVEQUFLQTs0RUFBQ3NILFNBQVE7c0ZBQXNCOzs7Ozs7c0ZBQ3JDLDhEQUFDdkgsdURBQUtBOzRFQUNKcUMsSUFBRzs0RUFDSHdELE9BQU96RSxRQUFRSSxJQUFJLENBQUNTLGVBQWU7NEVBQ25DeUYsVUFBVSxDQUFDQyxJQUFNakMsY0FBYyxRQUFRLG1CQUFtQmlDLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7NEVBQ3hFZ0MsYUFBWTs7Ozs7Ozs7Ozs7OzhFQUdoQiw4REFBQ3BEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ3pFLHVEQUFLQTs0RUFBQ3NILFNBQVE7c0ZBQXNCOzs7Ozs7c0ZBQ3JDLDhEQUFDdkgsdURBQUtBOzRFQUNKcUMsSUFBRzs0RUFDSHdELE9BQU96RSxRQUFRSSxJQUFJLENBQUNVLGVBQWU7NEVBQ25Dd0YsVUFBVSxDQUFDQyxJQUFNakMsY0FBYyxRQUFRLG1CQUFtQmlDLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7NEVBQ3hFZ0MsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBWTlCLDhEQUFDeEgsNkRBQVdBO3dCQUFDd0YsT0FBTTtrQ0FDakIsNEVBQUNuRyxxREFBSUE7OzhDQUNILDhEQUFDRywyREFBVUE7O3NEQUNULDhEQUFDQywwREFBU0E7NENBQUM0RSxXQUFVOzs4REFDbkIsOERBQUMzRCxxSUFBSUE7b0RBQUMyRCxXQUFVOzs7Ozs7Z0RBQWlCOzs7Ozs7O3NEQUduQyw4REFBQzlFLGdFQUFlQTtzREFBQzs7Ozs7Ozs7Ozs7OzhDQUluQiw4REFBQ0QsNERBQVdBO29DQUFDK0UsV0FBVTs7c0RBQ3JCLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEOztzRUFDQyw4REFBQ3hFLHVEQUFLQTs0REFBQ3NILFNBQVE7NERBQWtCN0MsV0FBVTtzRUFBd0I7Ozs7OztzRUFHbkUsOERBQUN3Qzs0REFBRXhDLFdBQVU7c0VBQXdCOzs7Ozs7Ozs7Ozs7OERBSXZDLDhEQUFDdkUseURBQU1BO29EQUNMa0MsSUFBRztvREFDSG1GLFNBQVNwRyxFQUFBQSxvQkFBQUEsUUFBUWUsUUFBUSxjQUFoQmYsd0NBQUFBLGtCQUFrQkssT0FBTyxLQUFJO29EQUN0Q2dHLGlCQUFpQixDQUFDRCxVQUFZOUIsY0FBYyxZQUFZLFdBQVc4Qjs7Ozs7Ozs7Ozs7O3dDQUl0RXBHLEVBQUFBLHFCQUFBQSxRQUFRZSxRQUFRLGNBQWhCZix5Q0FBQUEsbUJBQWtCSyxPQUFPLG1CQUN4Qiw4REFBQ2dEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUN6RSx1REFBS0E7b0VBQUNzSCxTQUFROzhFQUFnQjs7Ozs7OzhFQUMvQiw4REFBQ3ZILHVEQUFLQTtvRUFDSnFDLElBQUc7b0VBQ0h3RCxPQUFPekUsRUFBQUEscUJBQUFBLFFBQVFlLFFBQVEsY0FBaEJmLHlDQUFBQSxtQkFBa0JNLEtBQUssS0FBSTtvRUFDbENnRyxVQUFVLENBQUNDLElBQU1qQyxjQUFjLFlBQVksU0FBU2lDLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7b0VBQ2xFZ0MsYUFBWTs7Ozs7Ozs7Ozs7O3NFQUdoQiw4REFBQ3BEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ3pFLHVEQUFLQTtvRUFBQ3NILFNBQVE7OEVBQW1COzs7Ozs7OEVBQ2xDLDhEQUFDdkgsdURBQUtBO29FQUNKcUMsSUFBRztvRUFDSHdELE9BQU96RSxFQUFBQSxxQkFBQUEsUUFBUWUsUUFBUSxjQUFoQmYseUNBQUFBLG1CQUFrQk8sUUFBUSxLQUFJO29FQUNyQytGLFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsWUFBWSxZQUFZaUMsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSztvRUFDckVnQyxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBTWxCLDhEQUFDcEQ7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUN6RSx1REFBS0E7b0VBQUN5RSxXQUFVOzhFQUF3Qjs7Ozs7OzhFQUN6Qyw4REFBQzNFLHlEQUFNQTtvRUFBQ3FILFNBQVNyQjtvRUFBWWdDLE1BQUs7O3NGQUNoQyw4REFBQ25ILHFJQUFJQTs0RUFBQzhELFdBQVU7Ozs7Ozt3RUFBaUI7Ozs7Ozs7Ozs7Ozs7c0VBS3JDLDhEQUFDRDs0REFBSUMsV0FBVTt1RUFDWnRELHFCQUFBQSxRQUFRZSxRQUFRLGNBQWhCZiwwQ0FBQUEsMEJBQUFBLG1CQUFrQmdCLEtBQUssY0FBdkJoQiw4Q0FBQUEsd0JBQXlCb0YsR0FBRyxDQUFDLENBQUN3QixTQUFTQyxzQkFDdEMsOERBQUN2SSxxREFBSUE7b0VBQWtCZ0YsV0FBVTs7c0ZBQy9CLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUN3RDtvRkFBR3hELFdBQVU7O3dGQUFjO3dGQUFTdUQsUUFBUTs7Ozs7Ozs4RkFDN0MsOERBQUNsSSx5REFBTUE7b0ZBQ0xvSCxTQUFRO29GQUNSWSxNQUFLO29GQUNMWCxTQUFTLElBQU1oQixjQUFjNEIsUUFBUTNGLEVBQUU7OEZBRXZDLDRFQUFDeEIscUlBQU1BO3dGQUFDNkQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7c0ZBR3RCLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNEO29GQUFJQyxXQUFVOztzR0FDYiw4REFBQ3pFLHVEQUFLQTtzR0FBQzs7Ozs7O3NHQUNQLDhEQUFDRCx1REFBS0E7NEZBQ0o2RixPQUFPbUMsUUFBUXRHLEtBQUs7NEZBQ3BCZ0csVUFBVSxDQUFDQyxJQUFNcEIsY0FBY3lCLFFBQVEzRixFQUFFLEVBQUUsU0FBU3NGLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7NEZBQ2xFZ0MsYUFBWTs7Ozs7Ozs7Ozs7OzhGQUdoQiw4REFBQ3BEO29GQUFJQyxXQUFVOztzR0FDYiw4REFBQ3pFLHVEQUFLQTtzR0FBQzs7Ozs7O3NHQUNQLDhEQUFDa0k7NEZBQ0N0QyxPQUFPbUMsUUFBUTFGLElBQUk7NEZBQ25Cb0YsVUFBVSxDQUFDQyxJQUFNcEIsY0FBY3lCLFFBQVEzRixFQUFFLEVBQUUsUUFBUXNGLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7NEZBQ2pFbkIsV0FBVTs7OEdBRVYsOERBQUMwRDtvR0FBT3ZDLE9BQU07OEdBQVE7Ozs7Ozs4R0FDdEIsOERBQUN1QztvR0FBT3ZDLE9BQU07OEdBQWM7Ozs7Ozs4R0FDNUIsOERBQUN1QztvR0FBT3ZDLE9BQU07OEdBQVk7Ozs7Ozs4R0FDMUIsOERBQUN1QztvR0FBT3ZDLE9BQU07OEdBQVc7Ozs7Ozs4R0FDekIsOERBQUN1QztvR0FBT3ZDLE9BQU07OEdBQVM7Ozs7Ozs4R0FDdkIsOERBQUN1QztvR0FBT3ZDLE9BQU07OEdBQVE7Ozs7Ozs4R0FDdEIsOERBQUN1QztvR0FBT3ZDLE9BQU07OEdBQU07Ozs7Ozs4R0FDcEIsOERBQUN1QztvR0FBT3ZDLE9BQU07OEdBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRkFJaEMsOERBQUNwQjs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUN6RSx1REFBS0E7OEZBQUM7Ozs7Ozs4RkFDUCw4REFBQ0MsNkRBQVFBO29GQUNQMkYsT0FBT21DLFFBQVFwRyxXQUFXO29GQUMxQjhGLFVBQVUsQ0FBQ0MsSUFBTXBCLGNBQWN5QixRQUFRM0YsRUFBRSxFQUFFLGVBQWVzRixFQUFFQyxNQUFNLENBQUMvQixLQUFLO29GQUN4RWdDLGFBQVk7b0ZBQ1pDLE1BQU07Ozs7Ozs7Ozs7Ozs7bUVBNUNERSxRQUFRM0YsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQTBEckMsOERBQUNoQyw2REFBV0E7d0JBQUN3RixPQUFNO2tDQUNqQiw0RUFBQ25HLHFEQUFJQTs7OENBQ0gsOERBQUNHLDJEQUFVQTs7c0RBQ1QsOERBQUNDLDBEQUFTQTs0Q0FBQzRFLFdBQVU7OzhEQUNuQiw4REFBQzFELHFJQUFLQTtvREFBQzBELFdBQVU7Ozs7OztnREFBaUI7Ozs7Ozs7c0RBR3BDLDhEQUFDOUUsZ0VBQWVBO3NEQUFDOzs7Ozs7Ozs7Ozs7OENBSW5CLDhEQUFDRCw0REFBV0E7b0NBQUMrRSxXQUFVOztzREFDckIsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7O3NFQUNDLDhEQUFDeEUsdURBQUtBOzREQUFDc0gsU0FBUTs0REFBc0I3QyxXQUFVO3NFQUF3Qjs7Ozs7O3NFQUd2RSw4REFBQ3dDOzREQUFFeEMsV0FBVTtzRUFBd0I7Ozs7Ozs7Ozs7Ozs4REFJdkMsOERBQUN2RSx5REFBTUE7b0RBQ0xrQyxJQUFHO29EQUNIbUYsU0FBU3BHLEVBQUFBLHdCQUFBQSxRQUFRdUIsWUFBWSxjQUFwQnZCLDRDQUFBQSxzQkFBc0JLLE9BQU8sS0FBSTtvREFDMUNnRyxpQkFBaUIsQ0FBQ0QsVUFBWTlCLGNBQWMsZ0JBQWdCLFdBQVc4Qjs7Ozs7Ozs7Ozs7O3dDQUkxRXBHLEVBQUFBLHlCQUFBQSxRQUFRdUIsWUFBWSxjQUFwQnZCLDZDQUFBQSx1QkFBc0JLLE9BQU8sbUJBQzVCLDhEQUFDZ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ3pFLHVEQUFLQTtvRUFBQ3NILFNBQVE7OEVBQW9COzs7Ozs7OEVBQ25DLDhEQUFDdkgsdURBQUtBO29FQUNKcUMsSUFBRztvRUFDSHdELE9BQU96RSxFQUFBQSx5QkFBQUEsUUFBUXVCLFlBQVksY0FBcEJ2Qiw2Q0FBQUEsdUJBQXNCTSxLQUFLLEtBQUk7b0VBQ3RDZ0csVUFBVSxDQUFDQyxJQUFNakMsY0FBYyxnQkFBZ0IsU0FBU2lDLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7b0VBQ3RFZ0MsYUFBWTs7Ozs7Ozs7Ozs7O3NFQUdoQiw4REFBQ3BEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ3pFLHVEQUFLQTtvRUFBQ3NILFNBQVE7OEVBQXVCOzs7Ozs7OEVBQ3RDLDhEQUFDdkgsdURBQUtBO29FQUNKcUMsSUFBRztvRUFDSHdELE9BQU96RSxFQUFBQSx5QkFBQUEsUUFBUXVCLFlBQVksY0FBcEJ2Qiw2Q0FBQUEsdUJBQXNCTyxRQUFRLEtBQUk7b0VBQ3pDK0YsVUFBVSxDQUFDQyxJQUFNakMsY0FBYyxnQkFBZ0IsWUFBWWlDLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7b0VBQ3pFZ0MsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQU1sQiw4REFBQ3BEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDekUsdURBQUtBO29FQUFDeUUsV0FBVTs4RUFBd0I7Ozs7Ozs4RUFDekMsOERBQUMzRSx5REFBTUE7b0VBQUNxSCxTQUFTWDtvRUFBZ0JzQixNQUFLOztzRkFDcEMsOERBQUNuSCxxSUFBSUE7NEVBQUM4RCxXQUFVOzs7Ozs7d0VBQWlCOzs7Ozs7Ozs7Ozs7O3NFQUtyQyw4REFBQ0Q7NERBQUlDLFdBQVU7dUVBQ1p0RCx5QkFBQUEsUUFBUXVCLFlBQVksY0FBcEJ2Qiw4Q0FBQUEsOEJBQUFBLHVCQUFzQmdCLEtBQUssY0FBM0JoQixrREFBQUEsNEJBQTZCb0YsR0FBRyxDQUFDLENBQUM2QixhQUFhSixzQkFDOUMsOERBQUN2SSxxREFBSUE7b0VBQXNCZ0YsV0FBVTs7c0ZBQ25DLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUN3RDtvRkFBR3hELFdBQVU7O3dGQUFjO3dGQUFhdUQsUUFBUTs7Ozs7Ozs4RkFDakQsOERBQUNsSSx5REFBTUE7b0ZBQ0xvSCxTQUFRO29GQUNSWSxNQUFLO29GQUNMWCxTQUFTLElBQU1ULGtCQUFrQjBCLFlBQVloRyxFQUFFOzhGQUUvQyw0RUFBQ3hCLHFJQUFNQTt3RkFBQzZELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O3NGQUd0Qiw4REFBQ0Q7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUN6RSx1REFBS0E7c0dBQUM7Ozs7OztzR0FDUCw4REFBQ0QsdURBQUtBOzRGQUNKNkYsT0FBT3dDLFlBQVl6RixJQUFJOzRGQUN2QjhFLFVBQVUsQ0FBQ0MsSUFBTWYsa0JBQWtCeUIsWUFBWWhHLEVBQUUsRUFBRSxRQUFRc0YsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSzs0RkFDekVnQyxhQUFZOzs7Ozs7Ozs7Ozs7OEZBR2hCLDhEQUFDcEQ7b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDekUsdURBQUtBO3NHQUFDOzs7Ozs7c0dBQ1AsOERBQUNELHVEQUFLQTs0RkFDSjZGLE9BQU93QyxZQUFZeEYsSUFBSTs0RkFDdkI2RSxVQUFVLENBQUNDLElBQU1mLGtCQUFrQnlCLFlBQVloRyxFQUFFLEVBQUUsUUFBUXNGLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7NEZBQ3pFZ0MsYUFBWTs7Ozs7Ozs7Ozs7OzhGQUdoQiw4REFBQ3BEO29GQUFJQyxXQUFVOztzR0FDYiw4REFBQ3pFLHVEQUFLQTtzR0FBQzs7Ozs7O3NHQUNQLDhEQUFDRCx1REFBS0E7NEZBQ0o2RixPQUFPd0MsWUFBWXZGLE9BQU87NEZBQzFCNEUsVUFBVSxDQUFDQyxJQUFNZixrQkFBa0J5QixZQUFZaEcsRUFBRSxFQUFFLFdBQVdzRixFQUFFQyxNQUFNLENBQUMvQixLQUFLOzRGQUM1RWdDLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRkFJbEIsOERBQUNwRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNEO29GQUFJQyxXQUFVOztzR0FDYiw4REFBQ3pFLHVEQUFLQTtzR0FBQzs7Ozs7O3NHQUNQLDhEQUFDRCx1REFBS0E7NEZBQ0pzSSxNQUFLOzRGQUNMQyxLQUFJOzRGQUNKQyxLQUFJOzRGQUNKM0MsT0FBT3dDLFlBQVlyRixNQUFNOzRGQUN6QjBFLFVBQVUsQ0FBQ0MsSUFBTWYsa0JBQWtCeUIsWUFBWWhHLEVBQUUsRUFBRSxVQUFVb0csU0FBU2QsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSyxLQUFLOzs7Ozs7Ozs7Ozs7OEZBRzdGLDhEQUFDcEI7b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDekUsdURBQUtBO3NHQUFDOzs7Ozs7c0dBQ1AsOERBQUNELHVEQUFLQTs0RkFDSjZGLE9BQU93QyxZQUFZdEYsTUFBTTs0RkFDekIyRSxVQUFVLENBQUNDLElBQU1mLGtCQUFrQnlCLFlBQVloRyxFQUFFLEVBQUUsVUFBVXNGLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7NEZBQzNFZ0MsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NGQUlsQiw4REFBQ3BEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ3pFLHVEQUFLQTs4RkFBQzs7Ozs7OzhGQUNQLDhEQUFDQyw2REFBUUE7b0ZBQ1AyRixPQUFPd0MsWUFBWWpILE9BQU87b0ZBQzFCc0csVUFBVSxDQUFDQyxJQUFNZixrQkFBa0J5QixZQUFZaEcsRUFBRSxFQUFFLFdBQVdzRixFQUFFQyxNQUFNLENBQUMvQixLQUFLO29GQUM1RWdDLGFBQVk7b0ZBQ1pDLE1BQU07Ozs7Ozs7Ozs7Ozs7bUVBL0RETyxZQUFZaEcsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQTZFekMsOERBQUNoQyw2REFBV0E7d0JBQUN3RixPQUFNO2tDQUNqQiw0RUFBQ25HLHFEQUFJQTs7OENBQ0gsOERBQUNHLDJEQUFVQTs7c0RBQ1QsOERBQUNDLDBEQUFTQTtzREFBQzs7Ozs7O3NEQUNYLDhEQUFDRixnRUFBZUE7c0RBQUM7Ozs7Ozs7Ozs7Ozs4Q0FJbkIsOERBQUNELDREQUFXQTtvQ0FBQytFLFdBQVU7O3NEQUNyQiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDs7c0VBQ0MsOERBQUN4RSx1REFBS0E7NERBQUNzSCxTQUFROzREQUFhN0MsV0FBVTtzRUFBd0I7Ozs7OztzRUFHOUQsOERBQUN3Qzs0REFBRXhDLFdBQVU7c0VBQXdCOzs7Ozs7Ozs7Ozs7OERBSXZDLDhEQUFDdkUseURBQU1BO29EQUNMa0MsSUFBRztvREFDSG1GLFNBQVNwRyxFQUFBQSxlQUFBQSxRQUFRNkIsR0FBRyxjQUFYN0IsbUNBQUFBLGFBQWFLLE9BQU8sS0FBSTtvREFDakNnRyxpQkFBaUIsQ0FBQ0QsVUFBWTlCLGNBQWMsT0FBTyxXQUFXOEI7Ozs7Ozs7Ozs7Ozt3Q0FJakVwRyxFQUFBQSxnQkFBQUEsUUFBUTZCLEdBQUcsY0FBWDdCLG9DQUFBQSxjQUFhSyxPQUFPLG1CQUNuQiw4REFBQ2dEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUN6RSx1REFBS0E7b0VBQUNzSCxTQUFROzhFQUFXOzs7Ozs7OEVBQzFCLDhEQUFDdkgsdURBQUtBO29FQUNKcUMsSUFBRztvRUFDSHdELE9BQU96RSxFQUFBQSxnQkFBQUEsUUFBUTZCLEdBQUcsY0FBWDdCLG9DQUFBQSxjQUFhTSxLQUFLLEtBQUk7b0VBQzdCZ0csVUFBVSxDQUFDQyxJQUFNakMsY0FBYyxPQUFPLFNBQVNpQyxFQUFFQyxNQUFNLENBQUMvQixLQUFLO29FQUM3RGdDLGFBQVk7Ozs7Ozs7Ozs7OztzRUFHaEIsOERBQUNwRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUN6RSx1REFBS0E7b0VBQUNzSCxTQUFROzhFQUFjOzs7Ozs7OEVBQzdCLDhEQUFDdkgsdURBQUtBO29FQUNKcUMsSUFBRztvRUFDSHdELE9BQU96RSxFQUFBQSxnQkFBQUEsUUFBUTZCLEdBQUcsY0FBWDdCLG9DQUFBQSxjQUFhTyxRQUFRLEtBQUk7b0VBQ2hDK0YsVUFBVSxDQUFDQyxJQUFNakMsY0FBYyxPQUFPLFlBQVlpQyxFQUFFQyxNQUFNLENBQUMvQixLQUFLO29FQUNoRWdDLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFNbEIsOERBQUNwRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ3pFLHVEQUFLQTtvRUFBQ3lFLFdBQVU7OEVBQXdCOzs7Ozs7OEVBQ3pDLDhEQUFDM0UseURBQU1BO29FQUFDcUgsU0FBU1A7b0VBQVFrQixNQUFLOztzRkFDNUIsOERBQUNuSCxxSUFBSUE7NEVBQUM4RCxXQUFVOzs7Ozs7d0VBQWlCOzs7Ozs7Ozs7Ozs7O3NFQUtyQyw4REFBQ0Q7NERBQUlDLFdBQVU7dUVBQ1p0RCxnQkFBQUEsUUFBUTZCLEdBQUcsY0FBWDdCLHFDQUFBQSxxQkFBQUEsY0FBYWdCLEtBQUssY0FBbEJoQix5Q0FBQUEsbUJBQW9Cb0YsR0FBRyxDQUFDLENBQUN2RCxLQUFLZ0Ysc0JBQzdCLDhEQUFDdkkscURBQUlBO29FQUFjZ0YsV0FBVTs7c0ZBQzNCLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUN3RDtvRkFBR3hELFdBQVU7O3dGQUFjO3dGQUFLdUQsUUFBUTs7Ozs7Ozs4RkFDekMsOERBQUNsSSx5REFBTUE7b0ZBQ0xvSCxTQUFRO29GQUNSWSxNQUFLO29GQUNMWCxTQUFTLElBQU1MLFVBQVU5RCxJQUFJWixFQUFFOzhGQUUvQiw0RUFBQ3hCLHFJQUFNQTt3RkFBQzZELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O3NGQUd0Qiw4REFBQ0Q7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUN6RSx1REFBS0E7c0dBQUM7Ozs7OztzR0FDUCw4REFBQ0QsdURBQUtBOzRGQUNKNkYsT0FBTzVDLElBQUlDLFFBQVE7NEZBQ25Cd0UsVUFBVSxDQUFDQyxJQUFNWCxVQUFVL0QsSUFBSVosRUFBRSxFQUFFLFlBQVlzRixFQUFFQyxNQUFNLENBQUMvQixLQUFLOzRGQUM3RGdDLGFBQVk7Ozs7Ozs7Ozs7Ozs4RkFHaEIsOERBQUNwRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUN6RSx1REFBS0E7c0dBQUM7Ozs7OztzR0FDUCw4REFBQ0MsNkRBQVFBOzRGQUNQMkYsT0FBTzVDLElBQUlFLE1BQU07NEZBQ2pCdUUsVUFBVSxDQUFDQyxJQUFNWCxVQUFVL0QsSUFBSVosRUFBRSxFQUFFLFVBQVVzRixFQUFFQyxNQUFNLENBQUMvQixLQUFLOzRGQUMzRGdDLGFBQVk7NEZBQ1pDLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7bUVBMUJIN0UsSUFBSVosRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQXlDakMsOERBQUNoQyw2REFBV0E7d0JBQUN3RixPQUFNO2tDQUNqQiw0RUFBQ25HLHFEQUFJQTs7OENBQ0gsOERBQUNHLDJEQUFVQTs7c0RBQ1QsOERBQUNDLDBEQUFTQTtzREFBQzs7Ozs7O3NEQUNYLDhEQUFDRixnRUFBZUE7c0RBQUM7Ozs7Ozs7Ozs7Ozs4Q0FJbkIsOERBQUNELDREQUFXQTtvQ0FBQytFLFdBQVU7O3NEQUNyQiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDs7c0VBQ0MsOERBQUN4RSx1REFBS0E7NERBQUNzSCxTQUFROzREQUFhN0MsV0FBVTtzRUFBd0I7Ozs7OztzRUFHOUQsOERBQUN3Qzs0REFBRXhDLFdBQVU7c0VBQXdCOzs7Ozs7Ozs7Ozs7OERBSXZDLDhEQUFDdkUseURBQU1BO29EQUNMa0MsSUFBRztvREFDSG1GLFNBQVNwRyxFQUFBQSxlQUFBQSxRQUFRZ0MsR0FBRyxjQUFYaEMsbUNBQUFBLGFBQWFLLE9BQU8sS0FBSTtvREFDakNnRyxpQkFBaUIsQ0FBQ0QsVUFBWTlCLGNBQWMsT0FBTyxXQUFXOEI7Ozs7Ozs7Ozs7Ozt3Q0FJakVwRyxFQUFBQSxnQkFBQUEsUUFBUWdDLEdBQUcsY0FBWGhDLG9DQUFBQSxjQUFhSyxPQUFPLG1CQUNuQiw4REFBQ2dEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUN6RSx1REFBS0E7b0VBQUNzSCxTQUFROzhFQUFXOzs7Ozs7OEVBQzFCLDhEQUFDdkgsdURBQUtBO29FQUNKcUMsSUFBRztvRUFDSHdELE9BQU96RSxFQUFBQSxnQkFBQUEsUUFBUWdDLEdBQUcsY0FBWGhDLG9DQUFBQSxjQUFhTSxLQUFLLEtBQUk7b0VBQzdCZ0csVUFBVSxDQUFDQyxJQUFNakMsY0FBYyxPQUFPLFNBQVNpQyxFQUFFQyxNQUFNLENBQUMvQixLQUFLO29FQUM3RGdDLGFBQVk7Ozs7Ozs7Ozs7OztzRUFHaEIsOERBQUNwRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUN6RSx1REFBS0E7b0VBQUNzSCxTQUFROzhFQUFnQjs7Ozs7OzhFQUMvQiw4REFBQ3ZILHVEQUFLQTtvRUFDSnFDLElBQUc7b0VBQ0h3RCxPQUFPekUsRUFBQUEsZ0JBQUFBLFFBQVFnQyxHQUFHLGNBQVhoQyxvQ0FBQUEsY0FBYWlDLFVBQVUsS0FBSTtvRUFDbENxRSxVQUFVLENBQUNDLElBQU1qQyxjQUFjLE9BQU8sY0FBY2lDLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7b0VBQ2xFZ0MsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUtsQiw4REFBQ3BEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3pFLHVEQUFLQTs0REFBQ3NILFNBQVE7c0VBQWlCOzs7Ozs7c0VBQ2hDLDhEQUFDckgsNkRBQVFBOzREQUNQbUMsSUFBRzs0REFDSHdELE9BQU96RSxFQUFBQSxnQkFBQUEsUUFBUWdDLEdBQUcsY0FBWGhDLG9DQUFBQSxjQUFhUSxXQUFXLEtBQUk7NERBQ25DOEYsVUFBVSxDQUFDQyxJQUFNakMsY0FBYyxPQUFPLGVBQWVpQyxFQUFFQyxNQUFNLENBQUMvQixLQUFLOzREQUNuRWdDLGFBQVk7NERBQ1pDLE1BQU07Ozs7Ozs7Ozs7Ozs4REFJViw4REFBQ3JEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDekUsdURBQUtBO29FQUFDc0gsU0FBUTs4RUFBZ0I7Ozs7Ozs4RUFDL0IsOERBQUN2SCx1REFBS0E7b0VBQ0pxQyxJQUFHO29FQUNId0QsT0FBT3pFLEVBQUFBLGdCQUFBQSxRQUFRZ0MsR0FBRyxjQUFYaEMsb0NBQUFBLGNBQWFrQyxVQUFVLEtBQUk7b0VBQ2xDb0UsVUFBVSxDQUFDQyxJQUFNakMsY0FBYyxPQUFPLGNBQWNpQyxFQUFFQyxNQUFNLENBQUMvQixLQUFLO29FQUNsRWdDLGFBQVk7Ozs7Ozs7Ozs7OztzRUFHaEIsOERBQUNwRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUN6RSx1REFBS0E7b0VBQUNzSCxTQUFROzhFQUFxQjs7Ozs7OzhFQUNwQyw4REFBQ3ZILHVEQUFLQTtvRUFDSnFDLElBQUc7b0VBQ0h3RCxPQUFPekUsRUFBQUEsZ0JBQUFBLFFBQVFnQyxHQUFHLGNBQVhoQyxvQ0FBQUEsY0FBYWEsZUFBZSxLQUFJO29FQUN2Q3lGLFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsT0FBTyxtQkFBbUJpQyxFQUFFQyxNQUFNLENBQUMvQixLQUFLO29FQUN2RWdDLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVc1Qiw4REFBQ3hILDZEQUFXQTt3QkFBQ3dGLE9BQU07a0NBQ2pCLDRFQUFDbkcscURBQUlBOzs4Q0FDSCw4REFBQ0csMkRBQVVBOztzREFDVCw4REFBQ0MsMERBQVNBO3NEQUFDOzs7Ozs7c0RBQ1gsOERBQUNGLGdFQUFlQTtzREFBQzs7Ozs7Ozs7Ozs7OzhDQUluQiw4REFBQ0QsNERBQVdBO29DQUFDK0UsV0FBVTs7c0RBQ3JCLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3pFLHVEQUFLQTs0REFBQ3NILFNBQVE7c0VBQVc7Ozs7OztzRUFDMUIsOERBQUN2SCx1REFBS0E7NERBQ0pxQyxJQUFHOzREQUNId0QsT0FBT3pFLEVBQUFBLGVBQUFBLFFBQVE0QyxHQUFHLGNBQVg1QyxtQ0FBQUEsYUFBYU0sS0FBSyxLQUFJOzREQUM3QmdHLFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsT0FBTyxTQUFTaUMsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSzs0REFDN0RnQyxhQUFZOzs7Ozs7c0VBRWQsOERBQUNYOzREQUFFeEMsV0FBVTtzRUFBd0I7Ozs7Ozs7Ozs7Ozs4REFFdkMsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3pFLHVEQUFLQTs0REFBQ3NILFNBQVE7c0VBQWM7Ozs7OztzRUFDN0IsOERBQUN2SCx1REFBS0E7NERBQ0pxQyxJQUFHOzREQUNId0QsT0FBT3pFLEVBQUFBLGdCQUFBQSxRQUFRNEMsR0FBRyxjQUFYNUMsb0NBQUFBLGNBQWE2QyxRQUFRLEtBQUk7NERBQ2hDeUQsVUFBVSxDQUFDQyxJQUFNakMsY0FBYyxPQUFPLFlBQVlpQyxFQUFFQyxNQUFNLENBQUMvQixLQUFLOzREQUNoRWdDLGFBQVk7Ozs7OztzRUFFZCw4REFBQ1g7NERBQUV4QyxXQUFVO3NFQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUl6Qyw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDekUsdURBQUtBO29EQUFDc0gsU0FBUTs4REFBaUI7Ozs7Ozs4REFDaEMsOERBQUNySCw2REFBUUE7b0RBQ1BtQyxJQUFHO29EQUNId0QsT0FBT3pFLEVBQUFBLGdCQUFBQSxRQUFRNEMsR0FBRyxjQUFYNUMsb0NBQUFBLGNBQWFRLFdBQVcsS0FBSTtvREFDbkM4RixVQUFVLENBQUNDLElBQU1qQyxjQUFjLE9BQU8sZUFBZWlDLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7b0RBQ25FZ0MsYUFBWTtvREFDWkMsTUFBTTs7Ozs7OzhEQUVSLDhEQUFDWjtvREFBRXhDLFdBQVU7OERBQXdCOzs7Ozs7Ozs7Ozs7c0RBR3ZDLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUN6RSx1REFBS0E7b0RBQUNzSCxTQUFROzhEQUFhOzs7Ozs7OERBQzVCLDhEQUFDdkgsdURBQUtBO29EQUNKcUMsSUFBRztvREFDSHdELE9BQU96RSxFQUFBQSxnQkFBQUEsUUFBUTRDLEdBQUcsY0FBWDVDLG9DQUFBQSxjQUFhOEMsT0FBTyxLQUFJO29EQUMvQndELFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsT0FBTyxXQUFXaUMsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSztvREFDL0RnQyxhQUFZOzs7Ozs7OERBRWQsOERBQUNYO29EQUFFeEMsV0FBVTs4REFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUXJEO0dBdC9Cd0J4RDs7UUFDWTFCLHVEQUFVQTs7O0tBRHRCMEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL3N1cGVyLWFkbWluL2Ntcy9wYWdlLnRzeD9hN2NhIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VTZXNzaW9uIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xuaW1wb3J0IHsgcmVkaXJlY3QgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCdcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9pbnB1dCdcbmltcG9ydCB7IExhYmVsIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2xhYmVsJ1xuaW1wb3J0IHsgVGV4dGFyZWEgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdGV4dGFyZWEnXG5pbXBvcnQgeyBTd2l0Y2ggfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc3dpdGNoJ1xuaW1wb3J0IHsgVGFicywgVGFic0NvbnRlbnQsIFRhYnNMaXN0LCBUYWJzVHJpZ2dlciB9IGZyb20gJ0AvY29tcG9uZW50cy91aS90YWJzJ1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnXG5pbXBvcnQge1xuICBNb25pdG9yLFxuICBTYXZlLFxuICBSZWZyZXNoQ3csXG4gIEV5ZSxcbiAgVXBsb2FkLFxuICBQbHVzLFxuICBUcmFzaDIsXG4gIEVkaXQsXG4gIEltYWdlIGFzIEltYWdlSWNvbixcbiAgVHlwZSxcbiAgTGF5b3V0LFxuICBTdGFyLFxuICBVc2VycyxcbiAgQ2hlY2tDaXJjbGUsXG4gIEFycm93UmlnaHQsXG4gIFF1b3RlXG59IGZyb20gJ2x1Y2lkZS1yZWFjdCdcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSAncmVhY3QtaG90LXRvYXN0J1xuXG5pbnRlcmZhY2UgQ01TQ29udGVudCB7XG4gIC8vIEhlcm8gU2VjdGlvblxuICBoZXJvOiB7XG4gICAgZW5hYmxlZDogYm9vbGVhblxuICAgIHRpdGxlOiBzdHJpbmdcbiAgICBzdWJ0aXRsZTogc3RyaW5nXG4gICAgZGVzY3JpcHRpb246IHN0cmluZ1xuICAgIHByaW1hcnlDVEE6IHtcbiAgICAgIHRleHQ6IHN0cmluZ1xuICAgICAgbGluazogc3RyaW5nXG4gICAgfVxuICAgIHNlY29uZGFyeUNUQToge1xuICAgICAgdGV4dDogc3RyaW5nXG4gICAgICBsaW5rOiBzdHJpbmdcbiAgICB9XG4gICAgYmFja2dyb3VuZEltYWdlOiBzdHJpbmdcbiAgICBiYWNrZ3JvdW5kVmlkZW86IHN0cmluZ1xuICB9XG4gIFxuICAvLyBGZWF0dXJlcyBTZWN0aW9uXG4gIGZlYXR1cmVzOiB7XG4gICAgZW5hYmxlZDogYm9vbGVhblxuICAgIHRpdGxlOiBzdHJpbmdcbiAgICBzdWJ0aXRsZTogc3RyaW5nXG4gICAgaXRlbXM6IEFycmF5PHtcbiAgICAgIGlkOiBzdHJpbmdcbiAgICAgIHRpdGxlOiBzdHJpbmdcbiAgICAgIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgICAgIGljb246IHN0cmluZ1xuICAgICAgaW1hZ2U6IHN0cmluZ1xuICAgIH0+XG4gIH1cbiAgXG4gIC8vIFByaWNpbmcgU2VjdGlvblxuICBwcmljaW5nOiB7XG4gICAgZW5hYmxlZDogYm9vbGVhblxuICAgIHRpdGxlOiBzdHJpbmdcbiAgICBzdWJ0aXRsZTogc3RyaW5nXG4gICAgc2hvd1ByaWNpbmdUYWJsZTogYm9vbGVhblxuICAgIGN1c3RvbU1lc3NhZ2U6IHN0cmluZ1xuICB9XG4gIFxuICAvLyBUZXN0aW1vbmlhbHMgU2VjdGlvblxuICB0ZXN0aW1vbmlhbHM6IHtcbiAgICBlbmFibGVkOiBib29sZWFuXG4gICAgdGl0bGU6IHN0cmluZ1xuICAgIHN1YnRpdGxlOiBzdHJpbmdcbiAgICBpdGVtczogQXJyYXk8e1xuICAgICAgaWQ6IHN0cmluZ1xuICAgICAgbmFtZTogc3RyaW5nXG4gICAgICByb2xlOiBzdHJpbmdcbiAgICAgIGNvbXBhbnk6IHN0cmluZ1xuICAgICAgY29udGVudDogc3RyaW5nXG4gICAgICBhdmF0YXI6IHN0cmluZ1xuICAgICAgcmF0aW5nOiBudW1iZXJcbiAgICB9PlxuICB9XG4gIFxuICAvLyBGQVEgU2VjdGlvblxuICBmYXE6IHtcbiAgICBlbmFibGVkOiBib29sZWFuXG4gICAgdGl0bGU6IHN0cmluZ1xuICAgIHN1YnRpdGxlOiBzdHJpbmdcbiAgICBpdGVtczogQXJyYXk8e1xuICAgICAgaWQ6IHN0cmluZ1xuICAgICAgcXVlc3Rpb246IHN0cmluZ1xuICAgICAgYW5zd2VyOiBzdHJpbmdcbiAgICB9PlxuICB9XG4gIFxuICAvLyBDVEEgU2VjdGlvblxuICBjdGE6IHtcbiAgICBlbmFibGVkOiBib29sZWFuXG4gICAgdGl0bGU6IHN0cmluZ1xuICAgIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgICBidXR0b25UZXh0OiBzdHJpbmdcbiAgICBidXR0b25MaW5rOiBzdHJpbmdcbiAgICBiYWNrZ3JvdW5kSW1hZ2U6IHN0cmluZ1xuICB9XG4gIFxuICAvLyBGb290ZXJcbiAgZm9vdGVyOiB7XG4gICAgZW5hYmxlZDogYm9vbGVhblxuICAgIGNvbXBhbnlEZXNjcmlwdGlvbjogc3RyaW5nXG4gICAgbGlua3M6IEFycmF5PHtcbiAgICAgIGlkOiBzdHJpbmdcbiAgICAgIHRpdGxlOiBzdHJpbmdcbiAgICAgIGl0ZW1zOiBBcnJheTx7XG4gICAgICAgIGlkOiBzdHJpbmdcbiAgICAgICAgdGV4dDogc3RyaW5nXG4gICAgICAgIGxpbms6IHN0cmluZ1xuICAgICAgfT5cbiAgICB9PlxuICAgIHNvY2lhbExpbmtzOiB7XG4gICAgICB0d2l0dGVyOiBzdHJpbmdcbiAgICAgIGxpbmtlZGluOiBzdHJpbmdcbiAgICAgIGZhY2Vib29rOiBzdHJpbmdcbiAgICAgIGluc3RhZ3JhbTogc3RyaW5nXG4gICAgfVxuICAgIGNvcHlyaWdodFRleHQ6IHN0cmluZ1xuICB9XG4gIFxuICAvLyBTRU9cbiAgc2VvOiB7XG4gICAgdGl0bGU6IHN0cmluZ1xuICAgIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgICBrZXl3b3Jkczogc3RyaW5nXG4gICAgb2dJbWFnZTogc3RyaW5nXG4gIH1cbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ01TUGFnZSgpIHtcbiAgY29uc3QgeyBkYXRhOiBzZXNzaW9uLCBzdGF0dXMgfSA9IHVzZVNlc3Npb24oKVxuICBjb25zdCBbY29udGVudCwgc2V0Q29udGVudF0gPSB1c2VTdGF0ZTxDTVNDb250ZW50Pih7XG4gICAgaGVybzoge1xuICAgICAgZW5hYmxlZDogdHJ1ZSxcbiAgICAgIHRpdGxlOiAnQnVpbGQgWW91ciBTYWFTIEJ1c2luZXNzJyxcbiAgICAgIHN1YnRpdGxlOiAnVGhlIENvbXBsZXRlIFBsYXRmb3JtJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnRXZlcnl0aGluZyB5b3UgbmVlZCB0byBsYXVuY2gsIGdyb3csIGFuZCBzY2FsZSB5b3VyIFNhYVMgYnVzaW5lc3MuIEZyb20gY3VzdG9tZXIgbWFuYWdlbWVudCB0byBiaWxsaW5nLCB3ZVxcJ3ZlIGdvdCB5b3UgY292ZXJlZC4nLFxuICAgICAgcHJpbWFyeUNUQToge1xuICAgICAgICB0ZXh0OiAnU3RhcnQgRnJlZSBUcmlhbCcsXG4gICAgICAgIGxpbms6ICcvYXV0aC9zaWdudXAnXG4gICAgICB9LFxuICAgICAgc2Vjb25kYXJ5Q1RBOiB7XG4gICAgICAgIHRleHQ6ICdXYXRjaCBEZW1vJyxcbiAgICAgICAgbGluazogJy9kZW1vJ1xuICAgICAgfSxcbiAgICAgIGJhY2tncm91bmRJbWFnZTogJycsXG4gICAgICBiYWNrZ3JvdW5kVmlkZW86ICcnXG4gICAgfSxcbiAgICBmZWF0dXJlczoge1xuICAgICAgZW5hYmxlZDogdHJ1ZSxcbiAgICAgIHRpdGxlOiAnRXZlcnl0aGluZyBZb3UgTmVlZCcsXG4gICAgICBzdWJ0aXRsZTogJ1Bvd2VyZnVsIEZlYXR1cmVzJyxcbiAgICAgIGl0ZW1zOiBbXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJzEnLFxuICAgICAgICAgIHRpdGxlOiAnQ3VzdG9tZXIgTWFuYWdlbWVudCcsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICdNYW5hZ2UgeW91ciBjdXN0b21lcnMsIHRyYWNrIGludGVyYWN0aW9ucywgYW5kIGJ1aWxkIGxhc3RpbmcgcmVsYXRpb25zaGlwcy4nLFxuICAgICAgICAgIGljb246ICd1c2VycycsXG4gICAgICAgICAgaW1hZ2U6ICcnXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJzInLFxuICAgICAgICAgIHRpdGxlOiAnU3Vic2NyaXB0aW9uIEJpbGxpbmcnLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnQXV0b21hdGVkIGJpbGxpbmcsIGludm9pY2luZywgYW5kIHBheW1lbnQgcHJvY2Vzc2luZyBmb3IgcmVjdXJyaW5nIHJldmVudWUuJyxcbiAgICAgICAgICBpY29uOiAnY3JlZGl0LWNhcmQnLFxuICAgICAgICAgIGltYWdlOiAnJ1xuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICczJyxcbiAgICAgICAgICB0aXRsZTogJ0FuYWx5dGljcyAmIFJlcG9ydHMnLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnQ29tcHJlaGVuc2l2ZSBhbmFseXRpY3MgdG8gdHJhY2sgeW91ciBidXNpbmVzcyBwZXJmb3JtYW5jZSBhbmQgZ3Jvd3RoLicsXG4gICAgICAgICAgaWNvbjogJ2Jhci1jaGFydCcsXG4gICAgICAgICAgaW1hZ2U6ICcnXG4gICAgICAgIH1cbiAgICAgIF1cbiAgICB9LFxuICAgIHByaWNpbmc6IHtcbiAgICAgIGVuYWJsZWQ6IHRydWUsXG4gICAgICB0aXRsZTogJ1NpbXBsZSwgVHJhbnNwYXJlbnQgUHJpY2luZycsXG4gICAgICBzdWJ0aXRsZTogJ0Nob29zZSB0aGUgcGxhbiB0aGF0IGZpdHMgeW91ciBuZWVkcycsXG4gICAgICBzaG93UHJpY2luZ1RhYmxlOiB0cnVlLFxuICAgICAgY3VzdG9tTWVzc2FnZTogJydcbiAgICB9LFxuICAgIHRlc3RpbW9uaWFsczoge1xuICAgICAgZW5hYmxlZDogdHJ1ZSxcbiAgICAgIHRpdGxlOiAnV2hhdCBPdXIgQ3VzdG9tZXJzIFNheScsXG4gICAgICBzdWJ0aXRsZTogJ1RydXN0ZWQgYnkgdGhvdXNhbmRzIG9mIGJ1c2luZXNzZXMnLFxuICAgICAgaXRlbXM6IFtcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnMScsXG4gICAgICAgICAgbmFtZTogJ0pvaG4gU21pdGgnLFxuICAgICAgICAgIHJvbGU6ICdDRU8nLFxuICAgICAgICAgIGNvbXBhbnk6ICdUZWNoQ29ycCcsXG4gICAgICAgICAgY29udGVudDogJ1RoaXMgcGxhdGZvcm0gaGFzIHRyYW5zZm9ybWVkIGhvdyB3ZSBtYW5hZ2Ugb3VyIFNhYVMgYnVzaW5lc3MuIEhpZ2hseSByZWNvbW1lbmRlZCEnLFxuICAgICAgICAgIGF2YXRhcjogJycsXG4gICAgICAgICAgcmF0aW5nOiA1XG4gICAgICAgIH1cbiAgICAgIF1cbiAgICB9LFxuICAgIGZhcToge1xuICAgICAgZW5hYmxlZDogdHJ1ZSxcbiAgICAgIHRpdGxlOiAnRnJlcXVlbnRseSBBc2tlZCBRdWVzdGlvbnMnLFxuICAgICAgc3VidGl0bGU6ICdFdmVyeXRoaW5nIHlvdSBuZWVkIHRvIGtub3cnLFxuICAgICAgaXRlbXM6IFtcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnMScsXG4gICAgICAgICAgcXVlc3Rpb246ICdIb3cgZG8gSSBnZXQgc3RhcnRlZD8nLFxuICAgICAgICAgIGFuc3dlcjogJ1NpbXBseSBzaWduIHVwIGZvciBhIGZyZWUgdHJpYWwgYW5kIGZvbGxvdyBvdXIgb25ib2FyZGluZyBndWlkZSB0byBzZXQgdXAgeW91ciBhY2NvdW50LidcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnMicsXG4gICAgICAgICAgcXVlc3Rpb246ICdDYW4gSSBjYW5jZWwgYW55dGltZT8nLFxuICAgICAgICAgIGFuc3dlcjogJ1llcywgeW91IGNhbiBjYW5jZWwgeW91ciBzdWJzY3JpcHRpb24gYXQgYW55IHRpbWUuIE5vIGxvbmctdGVybSBjb250cmFjdHMgb3IgY2FuY2VsbGF0aW9uIGZlZXMuJ1xuICAgICAgICB9XG4gICAgICBdXG4gICAgfSxcbiAgICBjdGE6IHtcbiAgICAgIGVuYWJsZWQ6IHRydWUsXG4gICAgICB0aXRsZTogJ1JlYWR5IHRvIEdldCBTdGFydGVkPycsXG4gICAgICBkZXNjcmlwdGlvbjogJ0pvaW4gdGhvdXNhbmRzIG9mIGJ1c2luZXNzZXMgYWxyZWFkeSB1c2luZyBvdXIgcGxhdGZvcm0gdG8gZ3JvdyB0aGVpciBTYWFTLicsXG4gICAgICBidXR0b25UZXh0OiAnU3RhcnQgWW91ciBGcmVlIFRyaWFsJyxcbiAgICAgIGJ1dHRvbkxpbms6ICcvYXV0aC9zaWdudXAnLFxuICAgICAgYmFja2dyb3VuZEltYWdlOiAnJ1xuICAgIH0sXG4gICAgZm9vdGVyOiB7XG4gICAgICBlbmFibGVkOiB0cnVlLFxuICAgICAgY29tcGFueURlc2NyaXB0aW9uOiAnVGhlIGNvbXBsZXRlIFNhYVMgcGxhdGZvcm0gZm9yIG1vZGVybiBidXNpbmVzc2VzLicsXG4gICAgICBsaW5rczogW1xuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICcxJyxcbiAgICAgICAgICB0aXRsZTogJ1Byb2R1Y3QnLFxuICAgICAgICAgIGl0ZW1zOiBbXG4gICAgICAgICAgICB7IGlkOiAnMScsIHRleHQ6ICdGZWF0dXJlcycsIGxpbms6ICcvZmVhdHVyZXMnIH0sXG4gICAgICAgICAgICB7IGlkOiAnMicsIHRleHQ6ICdQcmljaW5nJywgbGluazogJy9wcmljaW5nJyB9LFxuICAgICAgICAgICAgeyBpZDogJzMnLCB0ZXh0OiAnU2VjdXJpdHknLCBsaW5rOiAnL3NlY3VyaXR5JyB9XG4gICAgICAgICAgXVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICcyJyxcbiAgICAgICAgICB0aXRsZTogJ0NvbXBhbnknLFxuICAgICAgICAgIGl0ZW1zOiBbXG4gICAgICAgICAgICB7IGlkOiAnMScsIHRleHQ6ICdBYm91dCcsIGxpbms6ICcvYWJvdXQnIH0sXG4gICAgICAgICAgICB7IGlkOiAnMicsIHRleHQ6ICdCbG9nJywgbGluazogJy9ibG9nJyB9LFxuICAgICAgICAgICAgeyBpZDogJzMnLCB0ZXh0OiAnQ2FyZWVycycsIGxpbms6ICcvY2FyZWVycycgfVxuICAgICAgICAgIF1cbiAgICAgICAgfVxuICAgICAgXSxcbiAgICAgIHNvY2lhbExpbmtzOiB7XG4gICAgICAgIHR3aXR0ZXI6ICcnLFxuICAgICAgICBsaW5rZWRpbjogJycsXG4gICAgICAgIGZhY2Vib29rOiAnJyxcbiAgICAgICAgaW5zdGFncmFtOiAnJ1xuICAgICAgfSxcbiAgICAgIGNvcHlyaWdodFRleHQ6ICfCqSAyMDI0IFlvdXIgQ29tcGFueS4gQWxsIHJpZ2h0cyByZXNlcnZlZC4nXG4gICAgfSxcbiAgICBzZW86IHtcbiAgICAgIHRpdGxlOiAnU2FhUyBQbGF0Zm9ybSAtIEJ1aWxkIFlvdXIgQnVzaW5lc3MnLFxuICAgICAgZGVzY3JpcHRpb246ICdUaGUgY29tcGxldGUgU2FhUyBwbGF0Zm9ybSBmb3IgbW9kZXJuIGJ1c2luZXNzZXMuIEN1c3RvbWVyIG1hbmFnZW1lbnQsIGJpbGxpbmcsIGFuYWx5dGljcywgYW5kIG1vcmUuJyxcbiAgICAgIGtleXdvcmRzOiAnc2FhcywgcGxhdGZvcm0sIGJ1c2luZXNzLCBjdXN0b21lciBtYW5hZ2VtZW50LCBiaWxsaW5nLCBhbmFseXRpY3MnLFxuICAgICAgb2dJbWFnZTogJydcbiAgICB9XG4gIH0pXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtzYXZpbmcsIHNldFNhdmluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3ByZXZpZXdNb2RlLCBzZXRQcmV2aWV3TW9kZV0gPSB1c2VTdGF0ZShmYWxzZSlcblxuICBpZiAoc3RhdHVzID09PSAnbG9hZGluZycpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMTIgdy0xMiBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMFwiPjwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgaWYgKHN0YXR1cyA9PT0gJ3VuYXV0aGVudGljYXRlZCcpIHtcbiAgICByZWRpcmVjdCgnL2F1dGgvc2lnbmluJylcbiAgfVxuXG4gIGlmIChzZXNzaW9uPy51c2VyPy5yb2xlICE9PSAnU1VQRVJfQURNSU4nKSB7XG4gICAgcmVkaXJlY3QoJy9kYXNoYm9hcmQnKVxuICB9XG5cbiAgY29uc3QgZmV0Y2hDb250ZW50ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3N1cGVyLWFkbWluL2NtcycpXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICBcbiAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0Q29udGVudCh7IC4uLmNvbnRlbnQsIC4uLmRhdGEuY29udGVudCB9KVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBDTVMgY29udGVudDonLCBlcnJvcilcbiAgICAgIHRvYXN0LmVycm9yKCdGYWlsZWQgdG8gbG9hZCBDTVMgY29udGVudCcpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3Qgc2F2ZUNvbnRlbnQgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldFNhdmluZyh0cnVlKVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9zdXBlci1hZG1pbi9jbXMnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xuICAgICAgICB9LFxuICAgICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShjb250ZW50KVxuICAgICAgfSlcblxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuICAgICAgXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHRvYXN0LnN1Y2Nlc3MoJ0NNUyBjb250ZW50IHNhdmVkIHN1Y2Nlc3NmdWxseScpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0b2FzdC5lcnJvcihkYXRhLmVycm9yIHx8ICdGYWlsZWQgdG8gc2F2ZSBDTVMgY29udGVudCcpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNhdmluZyBDTVMgY29udGVudDonLCBlcnJvcilcbiAgICAgIHRvYXN0LmVycm9yKCdGYWlsZWQgdG8gc2F2ZSBDTVMgY29udGVudCcpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFNhdmluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGZldGNoQ29udGVudCgpXG4gIH0sIFtdKVxuXG4gIGNvbnN0IHVwZGF0ZUNvbnRlbnQgPSAoc2VjdGlvbjoga2V5b2YgQ01TQ29udGVudCwga2V5OiBzdHJpbmcsIHZhbHVlOiBhbnkpID0+IHtcbiAgICBzZXRDb250ZW50KHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBbc2VjdGlvbl06IHtcbiAgICAgICAgLi4ucHJldltzZWN0aW9uXSxcbiAgICAgICAgW2tleV06IHZhbHVlXG4gICAgICB9XG4gICAgfSkpXG4gIH1cblxuICAvLyBGZWF0dXJlIG1hbmFnZW1lbnQgZnVuY3Rpb25zXG4gIGNvbnN0IGFkZEZlYXR1cmUgPSAoKSA9PiB7XG4gICAgY29uc3QgbmV3RmVhdHVyZSA9IHtcbiAgICAgIGlkOiBEYXRlLm5vdygpLnRvU3RyaW5nKCksXG4gICAgICB0aXRsZTogJ05ldyBGZWF0dXJlJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnRmVhdHVyZSBkZXNjcmlwdGlvbicsXG4gICAgICBpY29uOiAnc3RhcicsXG4gICAgICBpbWFnZTogJydcbiAgICB9XG4gICAgc2V0Q29udGVudChwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgZmVhdHVyZXM6IHtcbiAgICAgICAgLi4ucHJldi5mZWF0dXJlcyxcbiAgICAgICAgaXRlbXM6IFsuLi4ocHJldi5mZWF0dXJlcz8uaXRlbXMgfHwgW10pLCBuZXdGZWF0dXJlXVxuICAgICAgfVxuICAgIH0pKVxuICB9XG5cbiAgY29uc3QgcmVtb3ZlRmVhdHVyZSA9IChpZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0Q29udGVudChwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgZmVhdHVyZXM6IHtcbiAgICAgICAgLi4ucHJldi5mZWF0dXJlcyxcbiAgICAgICAgaXRlbXM6IHByZXYuZmVhdHVyZXM/Lml0ZW1zPy5maWx0ZXIoaXRlbSA9PiBpdGVtLmlkICE9PSBpZCkgfHwgW11cbiAgICAgIH1cbiAgICB9KSlcbiAgfVxuXG4gIGNvbnN0IHVwZGF0ZUZlYXR1cmUgPSAoaWQ6IHN0cmluZywga2V5OiBzdHJpbmcsIHZhbHVlOiBhbnkpID0+IHtcbiAgICBzZXRDb250ZW50KHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBmZWF0dXJlczoge1xuICAgICAgICAuLi5wcmV2LmZlYXR1cmVzLFxuICAgICAgICBpdGVtczogcHJldi5mZWF0dXJlcz8uaXRlbXM/Lm1hcChpdGVtID0+XG4gICAgICAgICAgaXRlbS5pZCA9PT0gaWQgPyB7IC4uLml0ZW0sIFtrZXldOiB2YWx1ZSB9IDogaXRlbVxuICAgICAgICApIHx8IFtdXG4gICAgICB9XG4gICAgfSkpXG4gIH1cblxuICAvLyBUZXN0aW1vbmlhbCBtYW5hZ2VtZW50IGZ1bmN0aW9uc1xuICBjb25zdCBhZGRUZXN0aW1vbmlhbCA9ICgpID0+IHtcbiAgICBjb25zdCBuZXdUZXN0aW1vbmlhbCA9IHtcbiAgICAgIGlkOiBEYXRlLm5vdygpLnRvU3RyaW5nKCksXG4gICAgICBuYW1lOiAnQ3VzdG9tZXIgTmFtZScsXG4gICAgICByb2xlOiAnQ0VPJyxcbiAgICAgIGNvbXBhbnk6ICdDb21wYW55IE5hbWUnLFxuICAgICAgY29udGVudDogJ0dyZWF0IHRlc3RpbW9uaWFsIGNvbnRlbnQuLi4nLFxuICAgICAgYXZhdGFyOiAnJyxcbiAgICAgIHJhdGluZzogNVxuICAgIH1cbiAgICBzZXRDb250ZW50KHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICB0ZXN0aW1vbmlhbHM6IHtcbiAgICAgICAgLi4ucHJldi50ZXN0aW1vbmlhbHMsXG4gICAgICAgIGl0ZW1zOiBbLi4uKHByZXYudGVzdGltb25pYWxzPy5pdGVtcyB8fCBbXSksIG5ld1Rlc3RpbW9uaWFsXVxuICAgICAgfVxuICAgIH0pKVxuICB9XG5cbiAgY29uc3QgcmVtb3ZlVGVzdGltb25pYWwgPSAoaWQ6IHN0cmluZykgPT4ge1xuICAgIHNldENvbnRlbnQocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIHRlc3RpbW9uaWFsczoge1xuICAgICAgICAuLi5wcmV2LnRlc3RpbW9uaWFscyxcbiAgICAgICAgaXRlbXM6IHByZXYudGVzdGltb25pYWxzPy5pdGVtcz8uZmlsdGVyKGl0ZW0gPT4gaXRlbS5pZCAhPT0gaWQpIHx8IFtdXG4gICAgICB9XG4gICAgfSkpXG4gIH1cblxuICBjb25zdCB1cGRhdGVUZXN0aW1vbmlhbCA9IChpZDogc3RyaW5nLCBrZXk6IHN0cmluZywgdmFsdWU6IGFueSkgPT4ge1xuICAgIHNldENvbnRlbnQocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIHRlc3RpbW9uaWFsczoge1xuICAgICAgICAuLi5wcmV2LnRlc3RpbW9uaWFscyxcbiAgICAgICAgaXRlbXM6IHByZXYudGVzdGltb25pYWxzPy5pdGVtcz8ubWFwKGl0ZW0gPT5cbiAgICAgICAgICBpdGVtLmlkID09PSBpZCA/IHsgLi4uaXRlbSwgW2tleV06IHZhbHVlIH0gOiBpdGVtXG4gICAgICAgICkgfHwgW11cbiAgICAgIH1cbiAgICB9KSlcbiAgfVxuXG4gIC8vIEZBUSBtYW5hZ2VtZW50IGZ1bmN0aW9uc1xuICBjb25zdCBhZGRGQVEgPSAoKSA9PiB7XG4gICAgY29uc3QgbmV3RkFRID0ge1xuICAgICAgaWQ6IERhdGUubm93KCkudG9TdHJpbmcoKSxcbiAgICAgIHF1ZXN0aW9uOiAnTmV3IHF1ZXN0aW9uPycsXG4gICAgICBhbnN3ZXI6ICdBbnN3ZXIgdG8gdGhlIHF1ZXN0aW9uLidcbiAgICB9XG4gICAgc2V0Q29udGVudChwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgZmFxOiB7XG4gICAgICAgIC4uLnByZXYuZmFxLFxuICAgICAgICBpdGVtczogWy4uLihwcmV2LmZhcT8uaXRlbXMgfHwgW10pLCBuZXdGQVFdXG4gICAgICB9XG4gICAgfSkpXG4gIH1cblxuICBjb25zdCByZW1vdmVGQVEgPSAoaWQ6IHN0cmluZykgPT4ge1xuICAgIHNldENvbnRlbnQocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIGZhcToge1xuICAgICAgICAuLi5wcmV2LmZhcSxcbiAgICAgICAgaXRlbXM6IHByZXYuZmFxPy5pdGVtcz8uZmlsdGVyKGl0ZW0gPT4gaXRlbS5pZCAhPT0gaWQpIHx8IFtdXG4gICAgICB9XG4gICAgfSkpXG4gIH1cblxuICBjb25zdCB1cGRhdGVGQVEgPSAoaWQ6IHN0cmluZywga2V5OiBzdHJpbmcsIHZhbHVlOiBhbnkpID0+IHtcbiAgICBzZXRDb250ZW50KHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBmYXE6IHtcbiAgICAgICAgLi4ucHJldi5mYXEsXG4gICAgICAgIGl0ZW1zOiBwcmV2LmZhcT8uaXRlbXM/Lm1hcChpdGVtID0+XG4gICAgICAgICAgaXRlbS5pZCA9PT0gaWQgPyB7IC4uLml0ZW0sIFtrZXldOiB2YWx1ZSB9IDogaXRlbVxuICAgICAgICApIHx8IFtdXG4gICAgICB9XG4gICAgfSkpXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IHNtOml0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gZ2FwLTRcIj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgPE1vbml0b3IgY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LWluZGlnby02MDBcIiAvPlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+TGFuZGluZyBQYWdlIENNUzwvaDE+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBtdC0xXCI+TWFuYWdlIHlvdXIgbGFuZGluZyBwYWdlIGNvbnRlbnQgYW5kIGxheW91dDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIG9uQ2xpY2s9e2ZldGNoQ29udGVudH0gZGlzYWJsZWQ9e2xvYWRpbmd9PlxuICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9e2BoLTQgdy00IG1yLTIgJHtsb2FkaW5nID8gJ2FuaW1hdGUtc3BpbicgOiAnJ31gfSAvPlxuICAgICAgICAgICAgUmVmcmVzaFxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBvbkNsaWNrPXsoKSA9PiBzZXRQcmV2aWV3TW9kZSghcHJldmlld01vZGUpfT5cbiAgICAgICAgICAgIDxFeWUgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgIHtwcmV2aWV3TW9kZSA/ICdFZGl0IE1vZGUnIDogJ1ByZXZpZXcnfVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b24gb25DbGljaz17c2F2ZUNvbnRlbnR9IGRpc2FibGVkPXtzYXZpbmd9PlxuICAgICAgICAgICAgPFNhdmUgY2xhc3NOYW1lPXtgaC00IHctNCBtci0yICR7c2F2aW5nID8gJ2FuaW1hdGUtc3BpbicgOiAnJ31gfSAvPlxuICAgICAgICAgICAgU2F2ZSBDaGFuZ2VzXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBDb250ZW50IE1hbmFnZW1lbnQgKi99XG4gICAgICA8VGFicyBkZWZhdWx0VmFsdWU9XCJoZXJvXCIgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIDxUYWJzTGlzdCBjbGFzc05hbWU9XCJncmlkIHctZnVsbCBncmlkLWNvbHMtN1wiPlxuICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cImhlcm9cIj5IZXJvPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJmZWF0dXJlc1wiPkZlYXR1cmVzPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJwcmljaW5nXCI+UHJpY2luZzwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwidGVzdGltb25pYWxzXCI+VGVzdGltb25pYWxzPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJmYXFcIj5GQVE8L1RhYnNUcmlnZ2VyPlxuICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cImN0YVwiPkNUQTwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwic2VvXCI+U0VPPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgPC9UYWJzTGlzdD5cblxuICAgICAgICB7LyogSGVybyBTZWN0aW9uICovfVxuICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJoZXJvXCI+XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxMYXlvdXQgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICBIZXJvIFNlY3Rpb25cbiAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAgQ29uZmlndXJlIHRoZSBtYWluIGhlcm8gc2VjdGlvbiBvZiB5b3VyIGxhbmRpbmcgcGFnZVxuICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC00IGJvcmRlciByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiaGVyb0VuYWJsZWRcIiBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgRW5hYmxlIEhlcm8gU2VjdGlvblxuICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICBTaG93IHRoZSBoZXJvIHNlY3Rpb24gb24geW91ciBsYW5kaW5nIHBhZ2VcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8U3dpdGNoXG4gICAgICAgICAgICAgICAgICBpZD1cImhlcm9FbmFibGVkXCJcbiAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2NvbnRlbnQuaGVyby5lbmFibGVkfVxuICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsoY2hlY2tlZCkgPT4gdXBkYXRlQ29udGVudCgnaGVybycsICdlbmFibGVkJywgY2hlY2tlZCl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAge2NvbnRlbnQuaGVyby5lbmFibGVkICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJoZXJvVGl0bGVcIj5NYWluIFRpdGxlPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiaGVyb1RpdGxlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250ZW50Lmhlcm8udGl0bGV9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUNvbnRlbnQoJ2hlcm8nLCAndGl0bGUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIllvdXIgbWFpbiBoZWFkbGluZVwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJoZXJvU3VidGl0bGVcIj5TdWJ0aXRsZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cImhlcm9TdWJ0aXRsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5oZXJvLnN1YnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdoZXJvJywgJ3N1YnRpdGxlJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTdXBwb3J0aW5nIGhlYWRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImhlcm9EZXNjcmlwdGlvblwiPkRlc2NyaXB0aW9uPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgaWQ9XCJoZXJvRGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250ZW50Lmhlcm8uZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdoZXJvJywgJ2Rlc2NyaXB0aW9uJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRGVzY3JpYmUgeW91ciBwcm9kdWN0IG9yIHNlcnZpY2VcIlxuICAgICAgICAgICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJwcmltYXJ5Q1RBVGV4dFwiPlByaW1hcnkgQnV0dG9uIFRleHQ8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJwcmltYXJ5Q1RBVGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5oZXJvLnByaW1hcnlDVEEudGV4dH1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlQ29udGVudCgnaGVybycsICdwcmltYXJ5Q1RBJywgeyAuLi5jb250ZW50Lmhlcm8ucHJpbWFyeUNUQSwgdGV4dDogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkdldCBTdGFydGVkXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInByaW1hcnlDVEFMaW5rXCI+UHJpbWFyeSBCdXR0b24gTGluazwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cInByaW1hcnlDVEFMaW5rXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250ZW50Lmhlcm8ucHJpbWFyeUNUQS5saW5rfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdoZXJvJywgJ3ByaW1hcnlDVEEnLCB7IC4uLmNvbnRlbnQuaGVyby5wcmltYXJ5Q1RBLCBsaW5rOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiL2F1dGgvc2lnbnVwXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInNlY29uZGFyeUNUQVRleHRcIj5TZWNvbmRhcnkgQnV0dG9uIFRleHQ8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJzZWNvbmRhcnlDVEFUZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250ZW50Lmhlcm8uc2Vjb25kYXJ5Q1RBLnRleHR9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUNvbnRlbnQoJ2hlcm8nLCAnc2Vjb25kYXJ5Q1RBJywgeyAuLi5jb250ZW50Lmhlcm8uc2Vjb25kYXJ5Q1RBLCB0ZXh0OiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiTGVhcm4gTW9yZVwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJzZWNvbmRhcnlDVEFMaW5rXCI+U2Vjb25kYXJ5IEJ1dHRvbiBMaW5rPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwic2Vjb25kYXJ5Q1RBTGlua1wiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5oZXJvLnNlY29uZGFyeUNUQS5saW5rfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdoZXJvJywgJ3NlY29uZGFyeUNUQScsIHsgLi4uY29udGVudC5oZXJvLnNlY29uZGFyeUNUQSwgbGluazogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIi9kZW1vXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogQmFja2dyb3VuZCBNZWRpYSAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIj5CYWNrZ3JvdW5kIE1lZGlhPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiaGVyb0JhY2tncm91bmRJbWFnZVwiPkJhY2tncm91bmQgSW1hZ2UgVVJMPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cImhlcm9CYWNrZ3JvdW5kSW1hZ2VcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5oZXJvLmJhY2tncm91bmRJbWFnZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdoZXJvJywgJ2JhY2tncm91bmRJbWFnZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJodHRwczovL2V4YW1wbGUuY29tL2hlcm8tYmcuanBnXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiaGVyb0JhY2tncm91bmRWaWRlb1wiPkJhY2tncm91bmQgVmlkZW8gVVJMPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cImhlcm9CYWNrZ3JvdW5kVmlkZW9cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5oZXJvLmJhY2tncm91bmRWaWRlb31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdoZXJvJywgJ2JhY2tncm91bmRWaWRlbycsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJodHRwczovL2V4YW1wbGUuY29tL2hlcm8tdmlkZW8ubXA0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9UYWJzQ29udGVudD5cblxuICAgICAgICB7LyogRmVhdHVyZXMgU2VjdGlvbiAqL31cbiAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwiZmVhdHVyZXNcIj5cbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPFN0YXIgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICBGZWF0dXJlcyBTZWN0aW9uXG4gICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgIENvbmZpZ3VyZSB0aGUgZmVhdHVyZXMgc2hvd2Nhc2Ugc2VjdGlvblxuICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC00IGJvcmRlciByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZmVhdHVyZXNFbmFibGVkXCIgY2xhc3NOYW1lPVwidGV4dC1iYXNlIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgIEVuYWJsZSBGZWF0dXJlcyBTZWN0aW9uXG4gICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIFNob3cgdGhlIGZlYXR1cmVzIHNlY3Rpb24gb24geW91ciBsYW5kaW5nIHBhZ2VcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8U3dpdGNoXG4gICAgICAgICAgICAgICAgICBpZD1cImZlYXR1cmVzRW5hYmxlZFwiXG4gICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb250ZW50LmZlYXR1cmVzPy5lbmFibGVkIHx8IGZhbHNlfVxuICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsoY2hlY2tlZCkgPT4gdXBkYXRlQ29udGVudCgnZmVhdHVyZXMnLCAnZW5hYmxlZCcsIGNoZWNrZWQpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHtjb250ZW50LmZlYXR1cmVzPy5lbmFibGVkICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJmZWF0dXJlc1RpdGxlXCI+U2VjdGlvbiBUaXRsZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cImZlYXR1cmVzVGl0bGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbnRlbnQuZmVhdHVyZXM/LnRpdGxlIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdmZWF0dXJlcycsICd0aXRsZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRXZlcnl0aGluZyBZb3UgTmVlZFwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJmZWF0dXJlc1N1YnRpdGxlXCI+U2VjdGlvbiBTdWJ0aXRsZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cImZlYXR1cmVzU3VidGl0bGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbnRlbnQuZmVhdHVyZXM/LnN1YnRpdGxlIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdmZWF0dXJlcycsICdzdWJ0aXRsZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiUG93ZXJmdWwgRmVhdHVyZXNcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBGZWF0dXJlIEl0ZW1zICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwidGV4dC1iYXNlIGZvbnQtbWVkaXVtXCI+RmVhdHVyZSBJdGVtczwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXthZGRGZWF0dXJlfSBzaXplPVwic21cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICBBZGQgRmVhdHVyZVxuICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtjb250ZW50LmZlYXR1cmVzPy5pdGVtcz8ubWFwKChmZWF0dXJlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPENhcmQga2V5PXtmZWF0dXJlLmlkfSBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+RmVhdHVyZSB7aW5kZXggKyAxfTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByZW1vdmVGZWF0dXJlKGZlYXR1cmUuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmFzaDIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsPkZlYXR1cmUgVGl0bGU8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmZWF0dXJlLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZlYXR1cmUoZmVhdHVyZS5pZCwgJ3RpdGxlJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkZlYXR1cmUgdGl0bGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsPkljb24gTmFtZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmZWF0dXJlLmljb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRmVhdHVyZShmZWF0dXJlLmlkLCAnaWNvbicsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHAtMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwidXNlcnNcIj5Vc2Vyczwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiY3JlZGl0LWNhcmRcIj5DcmVkaXQgQ2FyZDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYmFyLWNoYXJ0XCI+QmFyIENoYXJ0PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJidWlsZGluZ1wiPkJ1aWxkaW5nPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJzaGllbGRcIj5TaGllbGQ8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImdsb2JlXCI+R2xvYmU8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInphcFwiPlphcDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiZmlsZS10ZXh0XCI+RmlsZSBUZXh0PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yIG10LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWw+RmVhdHVyZSBEZXNjcmlwdGlvbjwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZmVhdHVyZS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRmVhdHVyZShmZWF0dXJlLmlkLCAnZGVzY3JpcHRpb24nLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkZlYXR1cmUgZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcm93cz17Mn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICA8L1RhYnNDb250ZW50PlxuXG4gICAgICAgIHsvKiBUZXN0aW1vbmlhbHMgU2VjdGlvbiAqL31cbiAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwidGVzdGltb25pYWxzXCI+XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxRdW90ZSBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIFRlc3RpbW9uaWFscyBTZWN0aW9uXG4gICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgIE1hbmFnZSBjdXN0b21lciB0ZXN0aW1vbmlhbHMgYW5kIHJldmlld3NcbiAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNCBib3JkZXIgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInRlc3RpbW9uaWFsc0VuYWJsZWRcIiBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgRW5hYmxlIFRlc3RpbW9uaWFscyBTZWN0aW9uXG4gICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIFNob3cgY3VzdG9tZXIgdGVzdGltb25pYWxzIG9uIHlvdXIgbGFuZGluZyBwYWdlXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPFN3aXRjaFxuICAgICAgICAgICAgICAgICAgaWQ9XCJ0ZXN0aW1vbmlhbHNFbmFibGVkXCJcbiAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2NvbnRlbnQudGVzdGltb25pYWxzPy5lbmFibGVkIHx8IGZhbHNlfVxuICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsoY2hlY2tlZCkgPT4gdXBkYXRlQ29udGVudCgndGVzdGltb25pYWxzJywgJ2VuYWJsZWQnLCBjaGVja2VkKX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7Y29udGVudC50ZXN0aW1vbmlhbHM/LmVuYWJsZWQgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInRlc3RpbW9uaWFsc1RpdGxlXCI+U2VjdGlvbiBUaXRsZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cInRlc3RpbW9uaWFsc1RpdGxlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250ZW50LnRlc3RpbW9uaWFscz8udGl0bGUgfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUNvbnRlbnQoJ3Rlc3RpbW9uaWFscycsICd0aXRsZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiV2hhdCBPdXIgQ3VzdG9tZXJzIFNheVwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJ0ZXN0aW1vbmlhbHNTdWJ0aXRsZVwiPlNlY3Rpb24gU3VidGl0bGU8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJ0ZXN0aW1vbmlhbHNTdWJ0aXRsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC50ZXN0aW1vbmlhbHM/LnN1YnRpdGxlIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCd0ZXN0aW1vbmlhbHMnLCAnc3VidGl0bGUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlRydXN0ZWQgYnkgdGhvdXNhbmRzIG9mIGJ1c2luZXNzZXNcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBUZXN0aW1vbmlhbCBJdGVtcyAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cInRleHQtYmFzZSBmb250LW1lZGl1bVwiPlRlc3RpbW9uaWFsczwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXthZGRUZXN0aW1vbmlhbH0gc2l6ZT1cInNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgQWRkIFRlc3RpbW9uaWFsXG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAge2NvbnRlbnQudGVzdGltb25pYWxzPy5pdGVtcz8ubWFwKCh0ZXN0aW1vbmlhbCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkIGtleT17dGVzdGltb25pYWwuaWR9IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5UZXN0aW1vbmlhbCB7aW5kZXggKyAxfTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByZW1vdmVUZXN0aW1vbmlhbCh0ZXN0aW1vbmlhbC5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRyYXNoMiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWw+Q3VzdG9tZXIgTmFtZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Rlc3RpbW9uaWFsLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlVGVzdGltb25pYWwodGVzdGltb25pYWwuaWQsICduYW1lJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkpvaG4gU21pdGhcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsPlJvbGU8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXt0ZXN0aW1vbmlhbC5yb2xlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZVRlc3RpbW9uaWFsKHRlc3RpbW9uaWFsLmlkLCAncm9sZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJDRU9cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsPkNvbXBhbnk8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXt0ZXN0aW1vbmlhbC5jb21wYW55fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZVRlc3RpbW9uaWFsKHRlc3RpbW9uaWFsLmlkLCAnY29tcGFueScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJUZWNoQ29ycFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00IG10LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsPlJhdGluZyAoMS01KTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbj1cIjFcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYXg9XCI1XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Rlc3RpbW9uaWFsLnJhdGluZ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVUZXN0aW1vbmlhbCh0ZXN0aW1vbmlhbC5pZCwgJ3JhdGluZycsIHBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKSB8fCA1KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbD5BdmF0YXIgVVJMIChvcHRpb25hbCk8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXt0ZXN0aW1vbmlhbC5hdmF0YXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlVGVzdGltb25pYWwodGVzdGltb25pYWwuaWQsICdhdmF0YXInLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiaHR0cHM6Ly9leGFtcGxlLmNvbS9hdmF0YXIuanBnXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMiBtdC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsPlRlc3RpbW9uaWFsIENvbnRlbnQ8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Rlc3RpbW9uaWFsLmNvbnRlbnR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZVRlc3RpbW9uaWFsKHRlc3RpbW9uaWFsLmlkLCAnY29udGVudCcsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiVGhpcyBwbGF0Zm9ybSBoYXMgdHJhbnNmb3JtZWQgb3VyIGJ1c2luZXNzLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9UYWJzQ29udGVudD5cblxuICAgICAgICB7LyogRkFRIFNlY3Rpb24gKi99XG4gICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cImZhcVwiPlxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGU+RkFRIFNlY3Rpb248L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICBNYW5hZ2UgZnJlcXVlbnRseSBhc2tlZCBxdWVzdGlvbnNcbiAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNCBib3JkZXIgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImZhcUVuYWJsZWRcIiBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgRW5hYmxlIEZBUSBTZWN0aW9uXG4gICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIFNob3cgRkFRIHNlY3Rpb24gb24geW91ciBsYW5kaW5nIHBhZ2VcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8U3dpdGNoXG4gICAgICAgICAgICAgICAgICBpZD1cImZhcUVuYWJsZWRcIlxuICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Y29udGVudC5mYXE/LmVuYWJsZWQgfHwgZmFsc2V9XG4gICAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9eyhjaGVja2VkKSA9PiB1cGRhdGVDb250ZW50KCdmYXEnLCAnZW5hYmxlZCcsIGNoZWNrZWQpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHtjb250ZW50LmZhcT8uZW5hYmxlZCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZmFxVGl0bGVcIj5TZWN0aW9uIFRpdGxlPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiZmFxVGl0bGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbnRlbnQuZmFxPy50aXRsZSB8fCAnJ31cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlQ29udGVudCgnZmFxJywgJ3RpdGxlJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJGcmVxdWVudGx5IEFza2VkIFF1ZXN0aW9uc1wiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJmYXFTdWJ0aXRsZVwiPlNlY3Rpb24gU3VidGl0bGU8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJmYXFTdWJ0aXRsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5mYXE/LnN1YnRpdGxlIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdmYXEnLCAnc3VidGl0bGUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkV2ZXJ5dGhpbmcgeW91IG5lZWQgdG8ga25vd1wiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIEZBUSBJdGVtcyAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cInRleHQtYmFzZSBmb250LW1lZGl1bVwiPkZBUSBJdGVtczwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXthZGRGQVF9IHNpemU9XCJzbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIEFkZCBGQVFcbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Y29udGVudC5mYXE/Lml0ZW1zPy5tYXAoKGZhcSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkIGtleT17ZmFxLmlkfSBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+RkFRIHtpbmRleCArIDF9PC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJlbW92ZUZBUShmYXEuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmFzaDIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWw+UXVlc3Rpb248L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmYXEucXVlc3Rpb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRkFRKGZhcS5pZCwgJ3F1ZXN0aW9uJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIldoYXQgaXMgeW91ciBxdWVzdGlvbj9cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsPkFuc3dlcjwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2ZhcS5hbnN3ZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRkFRKGZhcS5pZCwgJ2Fuc3dlcicsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJQcm92aWRlIGEgZGV0YWlsZWQgYW5zd2VyLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvVGFic0NvbnRlbnQ+XG5cbiAgICAgICAgey8qIENUQSBTZWN0aW9uICovfVxuICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJjdGFcIj5cbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlPkNhbGwtdG8tQWN0aW9uIFNlY3Rpb248L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICBDb25maWd1cmUgdGhlIGZpbmFsIGNhbGwtdG8tYWN0aW9uIHNlY3Rpb25cbiAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNCBib3JkZXIgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImN0YUVuYWJsZWRcIiBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgRW5hYmxlIENUQSBTZWN0aW9uXG4gICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIFNob3cgdGhlIGNhbGwtdG8tYWN0aW9uIHNlY3Rpb24gb24geW91ciBsYW5kaW5nIHBhZ2VcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8U3dpdGNoXG4gICAgICAgICAgICAgICAgICBpZD1cImN0YUVuYWJsZWRcIlxuICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Y29udGVudC5jdGE/LmVuYWJsZWQgfHwgZmFsc2V9XG4gICAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9eyhjaGVja2VkKSA9PiB1cGRhdGVDb250ZW50KCdjdGEnLCAnZW5hYmxlZCcsIGNoZWNrZWQpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHtjb250ZW50LmN0YT8uZW5hYmxlZCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiY3RhVGl0bGVcIj5DVEEgVGl0bGU8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJjdGFUaXRsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5jdGE/LnRpdGxlIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdjdGEnLCAndGl0bGUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlJlYWR5IHRvIEdldCBTdGFydGVkP1wiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJjdGFCdXR0b25UZXh0XCI+QnV0dG9uIFRleHQ8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJjdGFCdXR0b25UZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250ZW50LmN0YT8uYnV0dG9uVGV4dCB8fCAnJ31cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlQ29udGVudCgnY3RhJywgJ2J1dHRvblRleHQnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlN0YXJ0IFlvdXIgRnJlZSBUcmlhbFwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJjdGFEZXNjcmlwdGlvblwiPkNUQSBEZXNjcmlwdGlvbjwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxUZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY3RhRGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250ZW50LmN0YT8uZGVzY3JpcHRpb24gfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdjdGEnLCAnZGVzY3JpcHRpb24nLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJKb2luIHRob3VzYW5kcyBvZiBidXNpbmVzc2VzIGFscmVhZHkgdXNpbmcgb3VyIHBsYXRmb3JtLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiY3RhQnV0dG9uTGlua1wiPkJ1dHRvbiBMaW5rPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY3RhQnV0dG9uTGlua1wiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5jdGE/LmJ1dHRvbkxpbmsgfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUNvbnRlbnQoJ2N0YScsICdidXR0b25MaW5rJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIvYXV0aC9zaWdudXBcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiY3RhQmFja2dyb3VuZEltYWdlXCI+QmFja2dyb3VuZCBJbWFnZSBVUkw8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJjdGFCYWNrZ3JvdW5kSW1hZ2VcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbnRlbnQuY3RhPy5iYWNrZ3JvdW5kSW1hZ2UgfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUNvbnRlbnQoJ2N0YScsICdiYWNrZ3JvdW5kSW1hZ2UnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImh0dHBzOi8vZXhhbXBsZS5jb20vY3RhLWJnLmpwZ1wiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICA8L1RhYnNDb250ZW50PlxuXG4gICAgICAgIHsvKiBTRU8gU2VjdGlvbiAqL31cbiAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwic2VvXCI+XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZT5TRU8gU2V0dGluZ3M8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICBDb25maWd1cmUgU0VPIG1ldGEgdGFncyBhbmQgc29jaWFsIHNoYXJpbmdcbiAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInNlb1RpdGxlXCI+UGFnZSBUaXRsZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgaWQ9XCJzZW9UaXRsZVwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250ZW50LnNlbz8udGl0bGUgfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlQ29udGVudCgnc2VvJywgJ3RpdGxlJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNhYVMgUGxhdGZvcm0gLSBCdWlsZCBZb3VyIEJ1c2luZXNzXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5SZWNvbW1lbmRlZDogNTAtNjAgY2hhcmFjdGVyczwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJzZW9LZXl3b3Jkc1wiPktleXdvcmRzPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICBpZD1cInNlb0tleXdvcmRzXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbnRlbnQuc2VvPy5rZXl3b3JkcyB8fCAnJ31cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdzZW8nLCAna2V5d29yZHMnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwic2FhcywgcGxhdGZvcm0sIGJ1c2luZXNzLCBtYW5hZ2VtZW50XCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5Db21tYS1zZXBhcmF0ZWQga2V5d29yZHM8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJzZW9EZXNjcmlwdGlvblwiPk1ldGEgRGVzY3JpcHRpb248L0xhYmVsPlxuICAgICAgICAgICAgICAgIDxUZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgaWQ9XCJzZW9EZXNjcmlwdGlvblwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5zZW8/LmRlc2NyaXB0aW9uIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdzZW8nLCAnZGVzY3JpcHRpb24nLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlRoZSBjb21wbGV0ZSBTYWFTIHBsYXRmb3JtIGZvciBtb2Rlcm4gYnVzaW5lc3Nlcy4uLlwiXG4gICAgICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+UmVjb21tZW5kZWQ6IDE1MC0xNjAgY2hhcmFjdGVyczwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInNlb09nSW1hZ2VcIj5PcGVuIEdyYXBoIEltYWdlIFVSTDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICBpZD1cInNlb09nSW1hZ2VcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbnRlbnQuc2VvPy5vZ0ltYWdlIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdzZW8nLCAnb2dJbWFnZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiaHR0cHM6Ly9leGFtcGxlLmNvbS9vZy1pbWFnZS5qcGdcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+UmVjb21tZW5kZWQ6IDEyMDB4NjMwcHg8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvVGFic0NvbnRlbnQ+XG4gICAgICA8L1RhYnM+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVNlc3Npb24iLCJyZWRpcmVjdCIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmREZXNjcmlwdGlvbiIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCdXR0b24iLCJJbnB1dCIsIkxhYmVsIiwiVGV4dGFyZWEiLCJTd2l0Y2giLCJUYWJzIiwiVGFic0NvbnRlbnQiLCJUYWJzTGlzdCIsIlRhYnNUcmlnZ2VyIiwiTW9uaXRvciIsIlNhdmUiLCJSZWZyZXNoQ3ciLCJFeWUiLCJQbHVzIiwiVHJhc2gyIiwiTGF5b3V0IiwiU3RhciIsIlF1b3RlIiwidG9hc3QiLCJDTVNQYWdlIiwic2Vzc2lvbiIsImNvbnRlbnQiLCJkYXRhIiwic3RhdHVzIiwic2V0Q29udGVudCIsImhlcm8iLCJlbmFibGVkIiwidGl0bGUiLCJzdWJ0aXRsZSIsImRlc2NyaXB0aW9uIiwicHJpbWFyeUNUQSIsInRleHQiLCJsaW5rIiwic2Vjb25kYXJ5Q1RBIiwiYmFja2dyb3VuZEltYWdlIiwiYmFja2dyb3VuZFZpZGVvIiwiZmVhdHVyZXMiLCJpdGVtcyIsImlkIiwiaWNvbiIsImltYWdlIiwicHJpY2luZyIsInNob3dQcmljaW5nVGFibGUiLCJjdXN0b21NZXNzYWdlIiwidGVzdGltb25pYWxzIiwibmFtZSIsInJvbGUiLCJjb21wYW55IiwiYXZhdGFyIiwicmF0aW5nIiwiZmFxIiwicXVlc3Rpb24iLCJhbnN3ZXIiLCJjdGEiLCJidXR0b25UZXh0IiwiYnV0dG9uTGluayIsImZvb3RlciIsImNvbXBhbnlEZXNjcmlwdGlvbiIsImxpbmtzIiwic29jaWFsTGlua3MiLCJ0d2l0dGVyIiwibGlua2VkaW4iLCJmYWNlYm9vayIsImluc3RhZ3JhbSIsImNvcHlyaWdodFRleHQiLCJzZW8iLCJrZXl3b3JkcyIsIm9nSW1hZ2UiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInNhdmluZyIsInNldFNhdmluZyIsInByZXZpZXdNb2RlIiwic2V0UHJldmlld01vZGUiLCJkaXYiLCJjbGFzc05hbWUiLCJ1c2VyIiwiZmV0Y2hDb250ZW50IiwicmVzcG9uc2UiLCJmZXRjaCIsImpzb24iLCJzdWNjZXNzIiwiZXJyb3IiLCJjb25zb2xlIiwic2F2ZUNvbnRlbnQiLCJtZXRob2QiLCJoZWFkZXJzIiwiY3JlZGVudGlhbHMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInVwZGF0ZUNvbnRlbnQiLCJzZWN0aW9uIiwia2V5IiwidmFsdWUiLCJwcmV2IiwiYWRkRmVhdHVyZSIsIm5ld0ZlYXR1cmUiLCJEYXRlIiwibm93IiwidG9TdHJpbmciLCJyZW1vdmVGZWF0dXJlIiwiZmlsdGVyIiwiaXRlbSIsInVwZGF0ZUZlYXR1cmUiLCJtYXAiLCJhZGRUZXN0aW1vbmlhbCIsIm5ld1Rlc3RpbW9uaWFsIiwicmVtb3ZlVGVzdGltb25pYWwiLCJ1cGRhdGVUZXN0aW1vbmlhbCIsImFkZEZBUSIsIm5ld0ZBUSIsInJlbW92ZUZBUSIsInVwZGF0ZUZBUSIsImgxIiwicCIsInZhcmlhbnQiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJkZWZhdWx0VmFsdWUiLCJodG1sRm9yIiwiY2hlY2tlZCIsIm9uQ2hlY2tlZENoYW5nZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwicm93cyIsInNpemUiLCJmZWF0dXJlIiwiaW5kZXgiLCJoNCIsInNlbGVjdCIsIm9wdGlvbiIsInRlc3RpbW9uaWFsIiwidHlwZSIsIm1pbiIsIm1heCIsInBhcnNlSW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/super-admin/cms/page.tsx\n"));

/***/ })

});