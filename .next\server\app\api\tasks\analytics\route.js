"use strict";(()=>{var e={};e.id=5002,e.ids=[5002],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},43800:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>I,originalPathname:()=>h,patchFetch:()=>_,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>w});var r={};a.r(r),a.d(r,{GET:()=>l});var s=a(95419),n=a(69108),o=a(99678),i=a(78070),c=a(81355),d=a(3205),u=a(9108);async function l(e){try{let t=await (0,c.getServerSession)(d.L);if(!t?.user?.id||!t?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),r=a.get("period")||"30",s=new Date;s.setDate(s.getDate()-parseInt(r));let[n,o,l,p,m,y,g,I,w,h,_]=await Promise.all([u._.task.count({where:{companyId:t.user.companyId,createdAt:{gte:s}}}),u._.task.groupBy({by:["status"],where:{companyId:t.user.companyId,createdAt:{gte:s}},_count:{id:!0}}),u._.task.groupBy({by:["priority"],where:{companyId:t.user.companyId,createdAt:{gte:s}},_count:{id:!0}}),u._.task.groupBy({by:["assignedToId"],where:{companyId:t.user.companyId,createdAt:{gte:s}},_count:{id:!0}}),u._.task.count({where:{companyId:t.user.companyId,status:{not:"DONE"},dueDate:{lt:new Date}}}),u._.task.count({where:{companyId:t.user.companyId,status:"DONE",completedAt:{gte:s}}}),u._.task.groupBy({by:["type"],where:{companyId:t.user.companyId,createdAt:{gte:s}},_count:{id:!0}}),u._.task.groupBy({by:["category"],where:{companyId:t.user.companyId,createdAt:{gte:s},category:{not:null}},_count:{id:!0}}),Promise.resolve([{date:new Date().toISOString().split("T")[0],completed_count:await u._.task.count({where:{companyId:t.user.companyId,status:"DONE"}})}]),u._.task.findMany({where:{companyId:t.user.companyId,status:"DONE",completedAt:{gte:s},createdAt:{not:null},completedAt:{not:null}},select:{createdAt:!0,completedAt:!0}}),Promise.all([u._.task.count({where:{companyId:t.user.companyId,createdAt:{gte:new Date(new Date().setHours(0,0,0,0))}}}),u._.task.count({where:{companyId:t.user.companyId,status:"DONE",completedAt:{gte:new Date(new Date().setHours(0,0,0,0))}}}),u._.task.count({where:{companyId:t.user.companyId,status:{not:"DONE"},dueDate:{gte:new Date(new Date().setHours(0,0,0,0)),lt:new Date(new Date().setHours(23,59,59,999))}}})])]),k=p.map(e=>e.assignedToId).filter(Boolean),D=await u._.user.findMany({where:{id:{in:k},companyId:t.user.companyId},select:{id:!0,name:!0,email:!0}}),f=p.map(e=>({assignee:D.find(t=>t.id===e.assignedToId)||{id:e.assignedToId,name:"Unknown",email:""},count:e._count.id})),A=0;h.length>0&&(A=h.reduce((e,t)=>{if(t.createdAt&&t.completedAt){let a=new Date(t.completedAt).getTime()-new Date(t.createdAt).getTime();return e+a}return e},0)/h.length/864e5);let x=n>0?y/n*100:0,T=await u._.task.findMany({where:{companyId:t.user.companyId,status:{not:"DONE"},dueDate:{gte:new Date,lte:new Date(Date.now()+6048e5)}},include:{assignedTo:{select:{id:!0,name:!0,email:!0}},lead:{select:{id:!0,firstName:!0,lastName:!0,companyName:!0}},customer:{select:{id:!0,name:!0,company:!0}}},orderBy:{dueDate:"asc"},take:10}),E=await u._.task.findMany({where:{companyId:t.user.companyId,status:{not:"DONE"},priority:{in:["HIGH","URGENT","CRITICAL"]}},include:{assignedTo:{select:{id:!0,name:!0,email:!0}}},orderBy:[{priority:"desc"},{dueDate:"asc"}],take:10}),[q,v,b]=_;return i.Z.json({summary:{totalTasks:n,completedTasks:y,overdueTasks:m,completionRate:Math.round(100*x)/100,averageCompletionTime:Math.round(100*A)/100,tasksCreatedToday:q,tasksCompletedToday:v,tasksDueToday:b},tasksByStatus:o.map(e=>({status:e.status,count:e._count.id})),tasksByPriority:l.map(e=>({priority:e.priority,count:e._count.id})),tasksByAssignee:f,tasksByType:g.map(e=>({type:e.type,count:e._count.id})),tasksByCategory:I.map(e=>({category:e.category,count:e._count.id})),taskCompletionTrend:w,upcomingTasks:T,highPriorityTasks:E,period:parseInt(r)})}catch(e){return console.error("Error fetching task analytics:",e),i.Z.json({error:"Failed to fetch task analytics"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/tasks/analytics/route",pathname:"/api/tasks/analytics",filename:"route",bundlePath:"app/api/tasks/analytics/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\tasks\\analytics\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:m,staticGenerationAsyncStorage:y,serverHooks:g,headerHooks:I,staticGenerationBailout:w}=p,h="/api/tasks/analytics/route";function _(){return(0,o.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:y})}},3205:(e,t,a)=>{a.d(t,{L:()=>d});var r=a(86485),s=a(10375),n=a(50694),o=a(6521),i=a.n(o),c=a(9108);let d={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await c._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await c._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>s});let r=require("@prisma/client"),s=globalThis.prisma??new r.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520],()=>a(43800));module.exports=r})();