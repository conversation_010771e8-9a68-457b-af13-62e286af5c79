"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./components/dashboard/dashboard-layout.tsx":
/*!***************************************************!*\
  !*** ./components/dashboard/dashboard-layout.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardLayout: function() { return /* binding */ DashboardLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./sidebar */ \"(app-pages-browser)/./components/dashboard/sidebar.tsx\");\n/* harmony import */ var _header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./header */ \"(app-pages-browser)/./components/dashboard/header.tsx\");\n/* harmony import */ var _mobile_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./mobile-sidebar */ \"(app-pages-browser)/./components/dashboard/mobile-sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ DashboardLayout auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction DashboardLayout(param) {\n    let { children } = param;\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const user = (session === null || session === void 0 ? void 0 : session.user) ? {\n        name: session.user.name || \"\",\n        email: session.user.email || \"\",\n        image: session.user.image || \"\",\n        role: session.user.role || \"\",\n        company: (()=>{\n            try {\n                if (session.user.company && typeof session.user.company === \"object\") {\n                    return {\n                        name: String(session.user.company.name || \"\")\n                    };\n                }\n                return undefined;\n            } catch (error) {\n                console.error(\"Error processing company object:\", error);\n                return undefined;\n            }\n        })()\n    } : undefined;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex overflow-hidden bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:flex md:flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                    user: user,\n                    collapsed: sidebarCollapsed,\n                    onToggle: ()=>setSidebarCollapsed(!sidebarCollapsed)\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mobile_sidebar__WEBPACK_IMPORTED_MODULE_5__.MobileSidebar, {\n                isOpen: mobileMenuOpen,\n                onClose: ()=>setMobileMenuOpen(false),\n                user: user\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col w-0 flex-1 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header__WEBPACK_IMPORTED_MODULE_4__.Header, {\n                        user: user,\n                        onMobileMenuToggle: ()=>setMobileMenuOpen(true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 relative overflow-y-auto focus:outline-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-white border-t border-gray-200 px-4 py-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-center text-sm text-gray-500\",\n                                children: [\n                                    \"\\xa9 \",\n                                    new Date().getFullYear(),\n                                    \" Business SaaS. All rights reserved.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardLayout, \"RDHS+XkmFiB9FguH50Z6qakh5F4=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = DashboardLayout;\nvar _c;\n$RefreshReg$(_c, \"DashboardLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvZGFzaGJvYXJkL2Rhc2hib2FyZC1sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRWdDO0FBQ1k7QUFDVDtBQUNGO0FBQ2U7QUFNekMsU0FBU0ssZ0JBQWdCLEtBQWtDO1FBQWxDLEVBQUVDLFFBQVEsRUFBd0IsR0FBbEM7O0lBQzlCLE1BQU0sRUFBRUMsTUFBTUMsT0FBTyxFQUFFLEdBQUdQLDJEQUFVQTtJQUNwQyxNQUFNLENBQUNRLGtCQUFrQkMsb0JBQW9CLEdBQUdWLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ1csZ0JBQWdCQyxrQkFBa0IsR0FBR1osK0NBQVFBLENBQUM7SUFFckQsTUFBTWEsT0FBT0wsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTSyxJQUFJLElBQUc7UUFDM0JDLE1BQU1OLFFBQVFLLElBQUksQ0FBQ0MsSUFBSSxJQUFJO1FBQzNCQyxPQUFPUCxRQUFRSyxJQUFJLENBQUNFLEtBQUssSUFBSTtRQUM3QkMsT0FBT1IsUUFBUUssSUFBSSxDQUFDRyxLQUFLLElBQUk7UUFDN0JDLE1BQU1ULFFBQVFLLElBQUksQ0FBQ0ksSUFBSSxJQUFJO1FBQzNCQyxTQUFTLENBQUM7WUFDUixJQUFJO2dCQUNGLElBQUlWLFFBQVFLLElBQUksQ0FBQ0ssT0FBTyxJQUFJLE9BQU9WLFFBQVFLLElBQUksQ0FBQ0ssT0FBTyxLQUFLLFVBQVU7b0JBQ3BFLE9BQU87d0JBQUVKLE1BQU1LLE9BQU9YLFFBQVFLLElBQUksQ0FBQ0ssT0FBTyxDQUFDSixJQUFJLElBQUk7b0JBQUk7Z0JBQ3pEO2dCQUNBLE9BQU9NO1lBQ1QsRUFBRSxPQUFPQyxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsb0NBQW9DQTtnQkFDbEQsT0FBT0Q7WUFDVDtRQUNGO0lBQ0YsSUFBSUE7SUFFSixxQkFDRSw4REFBQ0c7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDdEIsNkNBQU9BO29CQUNOVyxNQUFNQTtvQkFDTlksV0FBV2hCO29CQUNYaUIsVUFBVSxJQUFNaEIsb0JBQW9CLENBQUNEOzs7Ozs7Ozs7OzswQkFLekMsOERBQUNMLDBEQUFhQTtnQkFDWnVCLFFBQVFoQjtnQkFDUmlCLFNBQVMsSUFBTWhCLGtCQUFrQjtnQkFDakNDLE1BQU1BOzs7Ozs7MEJBSVIsOERBQUNVO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ3JCLDJDQUFNQTt3QkFDTFUsTUFBTUE7d0JBQ05nQixvQkFBb0IsSUFBTWpCLGtCQUFrQjs7Ozs7O2tDQUc5Qyw4REFBQ2tCO3dCQUFLTixXQUFVO2tDQUNkLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ1psQjs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNUCw4REFBQ3lCO3dCQUFPUCxXQUFVO2tDQUNoQiw0RUFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNRO2dDQUFFUixXQUFVOztvQ0FBb0M7b0NBQzVDLElBQUlTLE9BQU9DLFdBQVc7b0NBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzFDO0dBbkVnQjdCOztRQUNZSix1REFBVUE7OztLQUR0QkkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9kYXNoYm9hcmQvZGFzaGJvYXJkLWxheW91dC50c3g/NmE3YyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHVzZVNlc3Npb24gfSBmcm9tICduZXh0LWF1dGgvcmVhY3QnXG5pbXBvcnQgeyBTaWRlYmFyIH0gZnJvbSAnLi9zaWRlYmFyJ1xuaW1wb3J0IHsgSGVhZGVyIH0gZnJvbSAnLi9oZWFkZXInXG5pbXBvcnQgeyBNb2JpbGVTaWRlYmFyIH0gZnJvbSAnLi9tb2JpbGUtc2lkZWJhcidcblxuaW50ZXJmYWNlIERhc2hib2FyZExheW91dFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZnVuY3Rpb24gRGFzaGJvYXJkTGF5b3V0KHsgY2hpbGRyZW4gfTogRGFzaGJvYXJkTGF5b3V0UHJvcHMpIHtcbiAgY29uc3QgeyBkYXRhOiBzZXNzaW9uIH0gPSB1c2VTZXNzaW9uKClcbiAgY29uc3QgW3NpZGViYXJDb2xsYXBzZWQsIHNldFNpZGViYXJDb2xsYXBzZWRdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFttb2JpbGVNZW51T3Blbiwgc2V0TW9iaWxlTWVudU9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgY29uc3QgdXNlciA9IHNlc3Npb24/LnVzZXIgPyB7XG4gICAgbmFtZTogc2Vzc2lvbi51c2VyLm5hbWUgfHwgJycsXG4gICAgZW1haWw6IHNlc3Npb24udXNlci5lbWFpbCB8fCAnJyxcbiAgICBpbWFnZTogc2Vzc2lvbi51c2VyLmltYWdlIHx8ICcnLFxuICAgIHJvbGU6IHNlc3Npb24udXNlci5yb2xlIHx8ICcnLFxuICAgIGNvbXBhbnk6ICgoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBpZiAoc2Vzc2lvbi51c2VyLmNvbXBhbnkgJiYgdHlwZW9mIHNlc3Npb24udXNlci5jb21wYW55ID09PSAnb2JqZWN0Jykge1xuICAgICAgICAgIHJldHVybiB7IG5hbWU6IFN0cmluZyhzZXNzaW9uLnVzZXIuY29tcGFueS5uYW1lIHx8ICcnKSB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZFxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcHJvY2Vzc2luZyBjb21wYW55IG9iamVjdDonLCBlcnJvcilcbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZFxuICAgICAgfVxuICAgIH0pKClcbiAgfSA6IHVuZGVmaW5lZFxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJoLXNjcmVlbiBmbGV4IG92ZXJmbG93LWhpZGRlbiBiZy1ncmF5LTEwMFwiPlxuICAgICAgey8qIERlc2t0b3AgU2lkZWJhciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmZsZXggbWQ6ZmxleC1zaHJpbmstMFwiPlxuICAgICAgICA8U2lkZWJhclxuICAgICAgICAgIHVzZXI9e3VzZXJ9XG4gICAgICAgICAgY29sbGFwc2VkPXtzaWRlYmFyQ29sbGFwc2VkfVxuICAgICAgICAgIG9uVG9nZ2xlPXsoKSA9PiBzZXRTaWRlYmFyQ29sbGFwc2VkKCFzaWRlYmFyQ29sbGFwc2VkKX1cbiAgICAgICAgLz5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTW9iaWxlIFNpZGViYXIgKi99XG4gICAgICA8TW9iaWxlU2lkZWJhclxuICAgICAgICBpc09wZW49e21vYmlsZU1lbnVPcGVufVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRNb2JpbGVNZW51T3BlbihmYWxzZSl9XG4gICAgICAgIHVzZXI9e3VzZXJ9XG4gICAgICAvPlxuXG4gICAgICB7LyogTWFpbiBDb250ZW50ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHctMCBmbGV4LTEgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIDxIZWFkZXJcbiAgICAgICAgICB1c2VyPXt1c2VyfVxuICAgICAgICAgIG9uTW9iaWxlTWVudVRvZ2dsZT17KCkgPT4gc2V0TW9iaWxlTWVudU9wZW4odHJ1ZSl9XG4gICAgICAgIC8+XG4gICAgICAgIFxuICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTEgcmVsYXRpdmUgb3ZlcmZsb3cteS1hdXRvIGZvY3VzOm91dGxpbmUtbm9uZVwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHktNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbWQ6cHgtOFwiPlxuICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9tYWluPlxuXG4gICAgICAgIHsvKiBGb290ZXIgKi99XG4gICAgICAgIDxmb290ZXIgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwIHB4LTQgcHktM1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IG1kOnB4LThcIj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICDCqSB7bmV3IERhdGUoKS5nZXRGdWxsWWVhcigpfSBCdXNpbmVzcyBTYWFTLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Zvb3Rlcj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VTZXNzaW9uIiwiU2lkZWJhciIsIkhlYWRlciIsIk1vYmlsZVNpZGViYXIiLCJEYXNoYm9hcmRMYXlvdXQiLCJjaGlsZHJlbiIsImRhdGEiLCJzZXNzaW9uIiwic2lkZWJhckNvbGxhcHNlZCIsInNldFNpZGViYXJDb2xsYXBzZWQiLCJtb2JpbGVNZW51T3BlbiIsInNldE1vYmlsZU1lbnVPcGVuIiwidXNlciIsIm5hbWUiLCJlbWFpbCIsImltYWdlIiwicm9sZSIsImNvbXBhbnkiLCJTdHJpbmciLCJ1bmRlZmluZWQiLCJlcnJvciIsImNvbnNvbGUiLCJkaXYiLCJjbGFzc05hbWUiLCJjb2xsYXBzZWQiLCJvblRvZ2dsZSIsImlzT3BlbiIsIm9uQ2xvc2UiLCJvbk1vYmlsZU1lbnVUb2dnbGUiLCJtYWluIiwiZm9vdGVyIiwicCIsIkRhdGUiLCJnZXRGdWxsWWVhciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dashboard/dashboard-layout.tsx\n"));

/***/ })

});