"use strict";(()=>{var e={};e.id=7712,e.ids=[7712],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},24538:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>h,originalPathname:()=>v,patchFetch:()=>f,requestAsyncStorage:()=>w,routeModule:()=>y,serverHooks:()=>I,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>_});var r={};a.r(r),a.d(r,{GET:()=>m,POST:()=>p});var s=a(95419),i=a(69108),n=a(99678),o=a(78070),l=a(81355),c=a(3205),u=a(9108),d=a(25252);async function m(e){try{let t=await (0,l.getServerSession)(c.L);if(!t?.user?.id||t?.user?.role!=="SUPER_ADMIN")return o.Z.json({error:"Super admin access required"},{status:403});let{searchParams:a}=new URL(e.url),r=parseInt(a.get("page")||"1"),s=parseInt(a.get("limit")||"50"),i=a.get("search")||"",n=a.get("status")||"",d=a.get("industry")||"",m=a.get("size")||"",p=(r-1)*s,y={};i&&(y.OR=[{name:{contains:i,mode:"insensitive"}},{email:{contains:i,mode:"insensitive"}},{industry:{contains:i,mode:"insensitive"}},{businessType:{contains:i,mode:"insensitive"}}]),n&&"all"!==n&&(y.status=n),d&&"all"!==d&&(y.industry=d),m&&"all"!==m&&(y.size=m);let[w,g]=await Promise.all([u._.company.findMany({where:y,include:{owner:{select:{id:!0,name:!0,email:!0,role:!0,status:!0,lastLoginAt:!0,loginCount:!0}},members:{select:{id:!0,name:!0,email:!0,role:!0,status:!0,lastLoginAt:!0}},_count:{select:{members:!0,customers:!0,quotations:!0,invoices:!0,contracts:!0,items:!0,activities:!0}}},orderBy:[{createdAt:"desc"}],skip:p,take:s}),u._.company.count({where:y})]),I=await Promise.all(w.map(async e=>{let[t,a,r,s]=await Promise.all([u._.activity.count({where:{companyId:e.id,createdAt:{gte:new Date(Date.now()-2592e6)}}}),u._.invoice.aggregate({where:{companyId:e.id,status:"PAID"},_sum:{total:!0}}),u._.user.count({where:{companyId:e.id,status:"ACTIVE"}}),u._.activity.findFirst({where:{companyId:e.id},orderBy:{createdAt:"desc"},select:{createdAt:!0,type:!0,title:!0}})]);return{id:e.id,name:e.name,email:e.email,phone:e.phone,address:e.address,city:e.city,state:e.state,country:e.country,postalCode:e.postalCode,website:e.website,logo:e.logo,industry:e.industry,size:e.size,businessType:e.businessType,status:e.status,taxId:e.taxId,registrationNumber:e.registrationNumber,owner:e.owner,members:e.members,counts:{members:e._count.members,customers:e._count.customers,quotations:e._count.quotations,invoices:e._count.invoices,contracts:e._count.contracts,items:e._count.items,activities:e._count.activities},metrics:{recentActivity:t,totalRevenue:Number(a._sum.total||0),activeUsers:r,lastActivity:s},createdAt:e.createdAt,updatedAt:e.updatedAt}}));return o.Z.json({companies:I,pagination:{page:r,limit:s,total:g,pages:Math.ceil(g/s)}})}catch(e){return console.error("Error fetching companies:",e),o.Z.json({error:"Failed to fetch companies"},{status:500})}}async function p(e){try{let t=await (0,l.getServerSession)(c.L);if(!t?.user?.id||t?.user?.role!=="SUPER_ADMIN")return o.Z.json({error:"Super admin access required"},{status:403});let{ownerEmail:r,ownerName:s,ownerPassword:i,...n}=await e.json();if(!n.name||!r||!s)return o.Z.json({error:"Company name, owner email, and owner name are required"},{status:400});let[d,m]=await Promise.all([u._.company.findFirst({where:{OR:[{name:n.name},{email:n.email}]}}),u._.user.findUnique({where:{email:r}})]);if(d)return o.Z.json({error:"Company with this name or email already exists"},{status:400});if(m)return o.Z.json({error:"User with this email already exists"},{status:400});let p=await u._.$transaction(async e=>{let o=await e.company.create({data:{...n,status:"ACTIVE"}}),l=i?await a(6521).hash(i,12):await a(6521).hash("tempPassword123",12),c=await e.user.create({data:{name:s,email:r,password:l,role:"ADMIN",status:"ACTIVE",companyId:o.id}});return await e.company.update({where:{id:o.id},data:{ownerId:c.id}}),await e.activity.create({data:{type:"ADMIN",title:"Company Created by Super Admin",description:`Company "${o.name}" was created by super admin`,companyId:o.id,createdById:t.user.id}}),{company:o,owner:c}});return o.Z.json({company:p.company,owner:{id:p.owner.id,name:p.owner.name,email:p.owner.email,role:p.owner.role},message:"Company created successfully"})}catch(e){return console.error("Error creating company:",e),o.Z.json({error:"Failed to create company"},{status:500})}}d.Ry({name:d.Z_().optional(),email:d.Z_().email().optional(),phone:d.Z_().optional(),address:d.Z_().optional(),city:d.Z_().optional(),state:d.Z_().optional(),country:d.Z_().optional(),postalCode:d.Z_().optional(),website:d.Z_().url().optional().or(d.i0("")),industry:d.Z_().optional(),size:d.Km(["STARTUP","SMALL","MEDIUM","LARGE","ENTERPRISE"]).optional(),businessType:d.Z_().optional(),status:d.Km(["ACTIVE","SUSPENDED","INACTIVE"]).optional(),taxId:d.Z_().optional(),registrationNumber:d.Z_().optional()});let y=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/super-admin/companies/route",pathname:"/api/super-admin/companies",filename:"route",bundlePath:"app/api/super-admin/companies/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\super-admin\\companies\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:w,staticGenerationAsyncStorage:g,serverHooks:I,headerHooks:h,staticGenerationBailout:_}=y,v="/api/super-admin/companies/route";function f(){return(0,n.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:g})}},3205:(e,t,a)=>{a.d(t,{L:()=>c});var r=a(86485),s=a(10375),i=a(50694),n=a(6521),o=a.n(n),l=a(9108);let c={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>s});let r=require("@prisma/client"),s=globalThis.prisma??new r.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520,5252],()=>a(24538));module.exports=r})();