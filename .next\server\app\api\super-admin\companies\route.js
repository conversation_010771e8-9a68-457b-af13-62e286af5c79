"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/super-admin/companies/route";
exports.ids = ["app/api/super-admin/companies/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fcompanies%2Froute&page=%2Fapi%2Fsuper-admin%2Fcompanies%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fcompanies%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fcompanies%2Froute&page=%2Fapi%2Fsuper-admin%2Fcompanies%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fcompanies%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_super_admin_companies_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/super-admin/companies/route.ts */ \"(rsc)/./app/api/super-admin/companies/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/super-admin/companies/route\",\n        pathname: \"/api/super-admin/companies\",\n        filename: \"route\",\n        bundlePath: \"app/api/super-admin/companies/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\super-admin\\\\companies\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_super_admin_companies_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/super-admin/companies/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fcompanies%2Froute&page=%2Fapi%2Fsuper-admin%2Fcompanies%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fcompanies%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/super-admin/companies/route.ts":
/*!************************************************!*\
  !*** ./app/api/super-admin/companies/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n\n\n\n\n\nconst companyUpdateSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    email: zod__WEBPACK_IMPORTED_MODULE_4__.string().email().optional(),\n    phone: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    address: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    city: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    state: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    country: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    postalCode: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    website: zod__WEBPACK_IMPORTED_MODULE_4__.string().url().optional().or(zod__WEBPACK_IMPORTED_MODULE_4__.literal(\"\")),\n    industry: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    size: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"STARTUP\",\n        \"SMALL\",\n        \"MEDIUM\",\n        \"LARGE\",\n        \"ENTERPRISE\"\n    ]).optional(),\n    businessType: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    status: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"ACTIVE\",\n        \"SUSPENDED\",\n        \"INACTIVE\"\n    ]).optional(),\n    taxId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    registrationNumber: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional()\n});\n// GET /api/super-admin/companies - Get all companies with admin details\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"50\");\n        const search = searchParams.get(\"search\") || \"\";\n        const status = searchParams.get(\"status\") || \"\";\n        const industry = searchParams.get(\"industry\") || \"\";\n        const size = searchParams.get(\"size\") || \"\";\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {};\n        if (search) {\n            where.OR = [\n                {\n                    name: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    email: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    industry: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    businessType: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                }\n            ];\n        }\n        if (status) {\n            where.status = status;\n        }\n        if (industry) {\n            where.industry = industry;\n        }\n        if (size) {\n            where.size = size;\n        }\n        const [companies, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.findMany({\n                where,\n                include: {\n                    owner: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            role: true,\n                            status: true,\n                            lastLoginAt: true,\n                            loginCount: true\n                        }\n                    },\n                    users: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            role: true,\n                            status: true,\n                            lastLoginAt: true\n                        }\n                    },\n                    _count: {\n                        select: {\n                            users: true,\n                            customers: true,\n                            quotations: true,\n                            invoices: true,\n                            contracts: true,\n                            items: true,\n                            activities: true\n                        }\n                    }\n                },\n                orderBy: [\n                    {\n                        createdAt: \"desc\"\n                    }\n                ],\n                skip,\n                take: limit\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count({\n                where\n            })\n        ]);\n        // Get additional metrics for each company\n        const companiesWithMetrics = await Promise.all(companies.map(async (company)=>{\n            const [recentActivity, totalRevenue, activeUsers, lastActivity] = await Promise.all([\n                // Recent activity count\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.count({\n                    where: {\n                        companyId: company.id,\n                        createdAt: {\n                            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)\n                        }\n                    }\n                }),\n                // Total revenue (from paid invoices)\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.aggregate({\n                    where: {\n                        companyId: company.id,\n                        status: \"PAID\"\n                    },\n                    _sum: {\n                        totalAmount: true\n                    }\n                }),\n                // Active users count\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                    where: {\n                        companyId: company.id,\n                        status: \"ACTIVE\"\n                    }\n                }),\n                // Last activity\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.findFirst({\n                    where: {\n                        companyId: company.id\n                    },\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    select: {\n                        createdAt: true,\n                        type: true,\n                        title: true\n                    }\n                })\n            ]);\n            return {\n                id: company.id,\n                name: company.name,\n                email: company.email,\n                phone: company.phone,\n                address: company.address,\n                city: company.city,\n                state: company.state,\n                country: company.country,\n                postalCode: company.postalCode,\n                website: company.website,\n                logo: company.logo,\n                industry: company.industry,\n                size: company.size,\n                businessType: company.businessType,\n                status: company.status,\n                taxId: company.taxId,\n                registrationNumber: company.registrationNumber,\n                owner: company.owner,\n                users: company.users,\n                counts: {\n                    users: company._count.users,\n                    customers: company._count.customers,\n                    quotations: company._count.quotations,\n                    invoices: company._count.invoices,\n                    contracts: company._count.contracts,\n                    items: company._count.items,\n                    activities: company._count.activities\n                },\n                metrics: {\n                    recentActivity,\n                    totalRevenue: Number(totalRevenue._sum.totalAmount || 0),\n                    activeUsers,\n                    lastActivity\n                },\n                createdAt: company.createdAt,\n                updatedAt: company.updatedAt\n            };\n        }));\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            companies: companiesWithMetrics,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching companies:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch companies\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/super-admin/companies - Create new company (admin only)\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { ownerEmail, ownerName, ownerPassword, ...companyData } = body;\n        // Validate required fields\n        if (!companyData.name || !ownerEmail || !ownerName) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Company name, owner email, and owner name are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if company or user already exists\n        const [existingCompany, existingUser] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.findFirst({\n                where: {\n                    OR: [\n                        {\n                            name: companyData.name\n                        },\n                        {\n                            email: companyData.email\n                        }\n                    ]\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n                where: {\n                    email: ownerEmail\n                }\n            })\n        ]);\n        if (existingCompany) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Company with this name or email already exists\"\n            }, {\n                status: 400\n            });\n        }\n        if (existingUser) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"User with this email already exists\"\n            }, {\n                status: 400\n            });\n        }\n        const result = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$transaction(async (tx)=>{\n            // Create company\n            const company = await tx.company.create({\n                data: {\n                    ...companyData,\n                    status: \"ACTIVE\"\n                }\n            });\n            // Create owner user\n            const hashedPassword = ownerPassword ? await (__webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\").hash)(ownerPassword, 12) : await (__webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\").hash)(\"tempPassword123\", 12);\n            const owner = await tx.user.create({\n                data: {\n                    name: ownerName,\n                    email: ownerEmail,\n                    password: hashedPassword,\n                    role: \"ADMIN\",\n                    status: \"ACTIVE\",\n                    companyId: company.id\n                }\n            });\n            // Update company with owner\n            await tx.company.update({\n                where: {\n                    id: company.id\n                },\n                data: {\n                    ownerId: owner.id\n                }\n            });\n            // Log admin action\n            await tx.activity.create({\n                data: {\n                    type: \"ADMIN\",\n                    title: \"Company Created by Super Admin\",\n                    description: `Company \"${company.name}\" was created by super admin`,\n                    companyId: company.id,\n                    createdById: session.user.id\n                }\n            });\n            return {\n                company,\n                owner\n            };\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            company: result.company,\n            owner: {\n                id: result.owner.id,\n                name: result.owner.name,\n                email: result.owner.email,\n                role: result.owner.role\n            },\n            message: \"Company created successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error creating company:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to create company\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/super-admin/companies/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fcompanies%2Froute&page=%2Fapi%2Fsuper-admin%2Fcompanies%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fcompanies%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();