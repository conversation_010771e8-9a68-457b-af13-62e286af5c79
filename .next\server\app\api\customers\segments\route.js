"use strict";(()=>{var e={};e.id=6673,e.ids=[6673],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},6780:(e,t,s)=>{s.r(t),s.d(t,{headerHooks:()=>y,originalPathname:()=>I,patchFetch:()=>v,requestAsyncStorage:()=>p,routeModule:()=>m,serverHooks:()=>h,staticGenerationAsyncStorage:()=>w,staticGenerationBailout:()=>g});var r={};s.r(r),s.d(r,{GET:()=>d});var a=s(95419),n=s(69108),o=s(99678),i=s(78070),c=s(81355),u=s(3205),l=s(9108);async function d(e){try{let t=await (0,c.getServerSession)(u.L);if(!t?.user?.id||!t?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let{searchParams:s}=new URL(e.url),r=s.get("type")||"all",a={highValue:{name:"High Value Customers",description:"Customers with total revenue over $10,000",where:{companyId:t.user.companyId,invoices:{some:{status:"PAID",total:{gte:1e4}}}}},vip:{name:"VIP Customers",description:"Customers with total revenue over $50,000",where:{companyId:t.user.companyId,invoices:{some:{status:"PAID",total:{gte:5e4}}}}},active:{name:"Active Customers",description:"Customers with activity in the last 30 days",where:{companyId:t.user.companyId,status:"ACTIVE",OR:[{activities:{some:{createdAt:{gte:new Date(Date.now()-2592e6)}}}},{invoices:{some:{createdAt:{gte:new Date(Date.now()-2592e6)}}}},{quotations:{some:{createdAt:{gte:new Date(Date.now()-2592e6)}}}}]}},inactive:{name:"Inactive Customers",description:"Customers with no activity in the last 90 days",where:{companyId:t.user.companyId,status:{not:"PROSPECT"},AND:[{activities:{none:{createdAt:{gte:new Date(Date.now()-7776e6)}}}},{invoices:{none:{createdAt:{gte:new Date(Date.now()-7776e6)}}}},{quotations:{none:{createdAt:{gte:new Date(Date.now()-7776e6)}}}}]}},new:{name:"New Customers",description:"Customers added in the last 30 days",where:{companyId:t.user.companyId,createdAt:{gte:new Date(Date.now()-2592e6)}}},prospects:{name:"Prospects",description:"Potential customers with no invoices yet",where:{companyId:t.user.companyId,status:"PROSPECT",invoices:{none:{}}}},atRisk:{name:"At Risk Customers",description:"Customers with no recent activity but previous purchases",where:{companyId:t.user.companyId,status:"ACTIVE",invoices:{some:{status:"PAID",createdAt:{gte:new Date(Date.now()-15552e6)}}},AND:[{activities:{none:{createdAt:{gte:new Date(Date.now()-5184e6)}}}},{invoices:{none:{createdAt:{gte:new Date(Date.now()-5184e6)}}}}]}},frequent:{name:"Frequent Buyers",description:"Customers with 3+ paid invoices",where:{companyId:t.user.companyId,invoices:{some:{status:"PAID"}}}}};if("all"!==r&&!a[r])return i.Z.json({error:"Invalid segment type"},{status:400});let n=[];if("all"===r)for(let[e,t]of Object.entries(a)){let s=await l._.customer.count({where:t.where}),r=[];r="frequent"===e?(await l._.customer.findMany({where:t.where,include:{_count:{select:{invoices:{where:{status:"PAID"}}}}}})).filter(e=>e._count.invoices>=3):await l._.customer.findMany({where:t.where,take:5,include:{invoices:{where:{status:"PAID"},select:{total:!0}}}}),n.push({id:e,name:t.name,description:t.description,count:"frequent"===e?r.length:s,customers:r.slice(0,5).map(e=>({id:e.id,name:e.name,email:e.email,company:e.company,status:e.status,totalRevenue:e.invoices?.reduce((e,t)=>e+t.total,0)||0}))})}else{let e=a[r],t=[],s=0;"frequent"===r?s=(t=(await l._.customer.findMany({where:e.where,include:{_count:{select:{invoices:{where:{status:"PAID"}}}},invoices:{where:{status:"PAID"},select:{total:!0}}}})).filter(e=>e._count.invoices>=3)).length:(s=await l._.customer.count({where:e.where}),t=await l._.customer.findMany({where:e.where,include:{invoices:{where:{status:"PAID"},select:{total:!0}}},orderBy:{createdAt:"desc"}})),n.push({id:r,name:e.name,description:e.description,count:s,customers:t.map(e=>({id:e.id,name:e.name,email:e.email,company:e.company,status:e.status,totalRevenue:e.invoices?.reduce((e,t)=>e+t.total,0)||0,createdAt:e.createdAt}))})}return i.Z.json({segments:n,totalSegments:Object.keys(a).length})}catch(e){return console.error("Error fetching customer segments:",e),i.Z.json({error:"Failed to fetch customer segments"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/customers/segments/route",pathname:"/api/customers/segments",filename:"route",bundlePath:"app/api/customers/segments/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\customers\\segments\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:p,staticGenerationAsyncStorage:w,serverHooks:h,headerHooks:y,staticGenerationBailout:g}=m,I="/api/customers/segments/route";function v(){return(0,o.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:w})}},3205:(e,t,s)=>{s.d(t,{L:()=>u});var r=s(86485),a=s(10375),n=s(50694),o=s(6521),i=s.n(o),c=s(9108);let u={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),s=t?.companyId;if(!s&&t){let e=await c._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(s=e?.id)&&await c._.user.update({where:{id:t.id},data:{companyId:s}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:s}}catch(e){return console.error("Authentication error:",e),null}}}),(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,s)=>{s.d(t,{_:()=>a});let r=require("@prisma/client"),a=globalThis.prisma??new r.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520],()=>s(6780));module.exports=r})();