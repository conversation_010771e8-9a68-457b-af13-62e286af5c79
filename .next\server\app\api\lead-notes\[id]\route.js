"use strict";(()=>{var e={};e.id=8147,e.ids=[8147],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},76492:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>j,originalPathname:()=>q,patchFetch:()=>_,requestAsyncStorage:()=>g,routeModule:()=>h,serverHooks:()=>x,staticGenerationAsyncStorage:()=>I,staticGenerationBailout:()=>v});var a={};t.r(a),t.d(a,{DELETE:()=>f,GET:()=>y,PUT:()=>w});var o=t(95419),s=t(69108),n=t(99678),i=t(78070),d=t(81355),l=t(3205),u=t(9108),c=t(25252),p=t(52178);let m=c.Ry({title:c.Z_().min(1,"Title is required").optional(),content:c.Z_().min(1,"Content is required").optional(),isPrivate:c.O7().optional()});async function y(e,{params:r}){try{let e=await (0,d.getServerSession)(l.L);if(!e?.user?.id||!e?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let t=await u._.leadNote.findFirst({where:{id:r.id,companyId:e.user.companyId,OR:[{isPrivate:!1},{isPrivate:!0,createdById:e.user.id}]},include:{lead:{select:{id:!0,firstName:!0,lastName:!0,companyName:!0}},createdBy:{select:{id:!0,name:!0,email:!0}}}});if(!t)return i.Z.json({error:"Note not found"},{status:404});return i.Z.json({note:t})}catch(e){return console.error("Error fetching note:",e),i.Z.json({error:"Failed to fetch note"},{status:500})}}async function w(e,{params:r}){try{let t=await (0,d.getServerSession)(l.L);if(!t?.user?.id||!t?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let a=await e.json(),o=m.parse(a);if(!await u._.leadNote.findFirst({where:{id:r.id,companyId:t.user.companyId,createdById:t.user.id}}))return i.Z.json({error:"Note not found or you do not have permission to edit it"},{status:404});let s=await u._.leadNote.update({where:{id:r.id},data:{...o,updatedAt:new Date},include:{lead:{select:{id:!0,firstName:!0,lastName:!0,companyName:!0}},createdBy:{select:{id:!0,name:!0,email:!0}}}});return i.Z.json({note:s})}catch(e){if(e instanceof p.jm)return i.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error updating note:",e),i.Z.json({error:"Failed to update note"},{status:500})}}async function f(e,{params:r}){try{let e=await (0,d.getServerSession)(l.L);if(!e?.user?.id||!e?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});if(!await u._.leadNote.findFirst({where:{id:r.id,companyId:e.user.companyId,createdById:e.user.id}}))return i.Z.json({error:"Note not found or you do not have permission to delete it"},{status:404});return await u._.leadNote.delete({where:{id:r.id}}),i.Z.json({message:"Note deleted successfully"})}catch(e){return console.error("Error deleting note:",e),i.Z.json({error:"Failed to delete note"},{status:500})}}let h=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/lead-notes/[id]/route",pathname:"/api/lead-notes/[id]",filename:"route",bundlePath:"app/api/lead-notes/[id]/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\lead-notes\\[id]\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:I,serverHooks:x,headerHooks:j,staticGenerationBailout:v}=h,q="/api/lead-notes/[id]/route";function _(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:I})}},3205:(e,r,t)=>{t.d(r,{L:()=>l});var a=t(86485),o=t(10375),s=t(50694),n=t(6521),i=t.n(n),d=t(9108);let l={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let r=await d._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),t=r?.companyId;if(!t&&r){let e=await d._.company.findFirst({where:{ownerId:r.id},select:{id:!0}});(t=e?.id)&&await d._.user.update({where:{id:r.id},data:{companyId:t}})}if(!r)return console.log("User not found:",e.email),null;if(!r.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,r.password))return console.log("Invalid password for user:",e.email),null;return await d._.user.update({where:{id:r.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",r.email),{id:r.id,email:r.email,name:r.name,role:r.role,companyId:t}}catch(e){return console.error("Authentication error:",e),null}}}),(0,o.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(console.log("JWT callback - user data:",{id:r.id,email:r.email,role:r.role,companyId:r.companyId}),e.role=r.role,e.companyId=r.companyId),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.companyId=r.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,r,t)=>{t.d(r,{_:()=>o});let a=require("@prisma/client"),o=globalThis.prisma??new a.PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,6521,2455,4520,5252],()=>t(76492));module.exports=a})();