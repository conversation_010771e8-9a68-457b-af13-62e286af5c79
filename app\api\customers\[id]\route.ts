import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for customer update
const customerUpdateSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  email: z.string().email('Invalid email').optional().nullable(),
  phone: z.string().optional().nullable(),
  company: z.string().optional().nullable(),
  address: z.string().optional().nullable(),
  city: z.string().optional().nullable(),
  state: z.string().optional().nullable(),
  country: z.string().optional().nullable(),
  postalCode: z.string().optional().nullable(),
  industry: z.string().optional().nullable(),
  website: z.string().url('Invalid website URL').optional().nullable().or(z.literal('')),
  notes: z.string().optional().nullable(),
  tags: z.array(z.string()).optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'PROSPECT']).optional()
})

// GET /api/customers/[id] - Get single customer
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const customer = await prisma.customer.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      },
      include: {
        createdBy: {
          select: { name: true, email: true }
        },
        leads: {
          orderBy: { createdAt: 'desc' },
          take: 5,
          include: {
            createdBy: { select: { name: true } }
          }
        },
        quotations: {
          orderBy: { createdAt: 'desc' },
          take: 5,
          include: {
            createdBy: { select: { name: true } }
          }
        },
        invoices: {
          orderBy: { createdAt: 'desc' },
          take: 5,
          include: {
            createdBy: { select: { name: true } }
          }
        },
        activities: {
          orderBy: { createdAt: 'desc' },
          take: 10,
          include: {
            createdBy: { select: { name: true } }
          }
        },
        _count: {
          select: {
            leads: true,
            quotations: true,
            invoices: true,
            contracts: true,
            activities: true
          }
        }
      }
    })

    if (!customer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
    }

    return NextResponse.json(customer)
  } catch (error) {
    console.error('Error fetching customer:', error)
    return NextResponse.json(
      { error: 'Failed to fetch customer' },
      { status: 500 }
    )
  }
}

// PUT /api/customers/[id] - Update customer
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = customerUpdateSchema.parse(body)

    // Check if customer exists and belongs to user's company
    const existingCustomer = await prisma.customer.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      }
    })

    if (!existingCustomer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
    }

    // Check if email is being changed and if it conflicts with another customer
    if (validatedData.email && validatedData.email !== existingCustomer.email) {
      const emailConflict = await prisma.customer.findFirst({
        where: {
          email: validatedData.email,
          companyId: session.user.companyId || undefined,
          id: { not: params.id }
        }
      })

      if (emailConflict) {
        return NextResponse.json(
          { error: 'Customer with this email already exists' },
          { status: 400 }
        )
      }
    }

    const customer = await prisma.customer.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        createdBy: {
          select: { name: true, email: true }
        },
        _count: {
          select: {
            leads: true,
            quotations: true,
            invoices: true,
            activities: true
          }
        }
      }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Customer Updated',
        description: `Customer "${customer.name}" was updated`,
        customerId: customer.id,
        companyId: session.user.companyId!,
        createdById: session.user.id
      }
    })

    return NextResponse.json(customer)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating customer:', error)
    return NextResponse.json(
      { error: 'Failed to update customer' },
      { status: 500 }
    )
  }
}

// DELETE /api/customers/[id] - Delete customer
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if customer exists and belongs to user's company
    const customer = await prisma.customer.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      },
      include: {
        _count: {
          select: {
            leads: true,
            quotations: true,
            invoices: true,
            contracts: true
          }
        }
      }
    })

    if (!customer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
    }

    // Check if customer has related records
    const hasRelatedRecords = customer._count.leads > 0 || 
                             customer._count.quotations > 0 || 
                             customer._count.invoices > 0 || 
                             customer._count.contracts > 0

    if (hasRelatedRecords) {
      return NextResponse.json(
        { 
          error: 'Cannot delete customer with existing leads, quotations, invoices, or contracts',
          details: customer._count
        },
        { status: 400 }
      )
    }

    await prisma.customer.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Customer deleted successfully' })
  } catch (error) {
    console.error('Error deleting customer:', error)
    return NextResponse.json(
      { error: 'Failed to delete customer' },
      { status: 500 }
    )
  }
}
