import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for saved filters
const savedFilterSchema = z.object({
  name: z.string().min(1, 'Filter name is required'),
  description: z.string().optional(),
  filters: z.record(z.any()), // The actual filter parameters
  isPublic: z.boolean().default(false), // Whether other team members can use this filter
  isDefault: z.boolean().default(false) // Whether this is the user's default filter
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const includePublic = searchParams.get('includePublic') === 'true'

    // Get user's saved filters and public filters
    const where: any = {
      companyId: session.user.companyId,
      OR: [
        { createdById: session.user.id }, // User's own filters
        ...(includePublic ? [{ isPublic: true }] : []) // Public filters if requested
      ]
    }

    const savedFilters = await prisma.leadSavedFilter.findMany({
      where,
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: [
        { isDefault: 'desc' }, // Default filters first
        { createdAt: 'desc' }
      ]
    })

    return NextResponse.json({ savedFilters })

  } catch (error) {
    console.error('Error fetching saved filters:', error)
    return NextResponse.json(
      { error: 'Failed to fetch saved filters' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = savedFilterSchema.parse(body)

    // If this is being set as default, unset any existing default for this user
    if (validatedData.isDefault) {
      await prisma.leadSavedFilter.updateMany({
        where: {
          createdById: session.user.id,
          companyId: session.user.companyId,
          isDefault: true
        },
        data: {
          isDefault: false
        }
      })
    }

    const savedFilter = await prisma.leadSavedFilter.create({
      data: {
        ...validatedData,
        companyId: session.user.companyId,
        createdById: session.user.id
      },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    return NextResponse.json({ savedFilter }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating saved filter:', error)
    return NextResponse.json(
      { error: 'Failed to create saved filter' },
      { status: 500 }
    )
  }
}

// Individual saved filter operations
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const filterId = searchParams.get('id')

    if (!filterId) {
      return NextResponse.json({ error: 'Filter ID is required' }, { status: 400 })
    }

    const body = await request.json()
    const validatedData = savedFilterSchema.parse(body)

    // Check if filter exists and user has permission to edit
    const existingFilter = await prisma.leadSavedFilter.findFirst({
      where: {
        id: filterId,
        companyId: session.user.companyId,
        createdById: session.user.id // Only creator can edit
      }
    })

    if (!existingFilter) {
      return NextResponse.json({ 
        error: 'Filter not found or you do not have permission to edit it' 
      }, { status: 404 })
    }

    // If this is being set as default, unset any existing default for this user
    if (validatedData.isDefault && !existingFilter.isDefault) {
      await prisma.leadSavedFilter.updateMany({
        where: {
          createdById: session.user.id,
          companyId: session.user.companyId,
          isDefault: true,
          id: { not: filterId }
        },
        data: {
          isDefault: false
        }
      })
    }

    const updatedFilter = await prisma.leadSavedFilter.update({
      where: { id: filterId },
      data: {
        ...validatedData,
        updatedAt: new Date()
      },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    return NextResponse.json({ savedFilter: updatedFilter })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating saved filter:', error)
    return NextResponse.json(
      { error: 'Failed to update saved filter' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const filterId = searchParams.get('id')

    if (!filterId) {
      return NextResponse.json({ error: 'Filter ID is required' }, { status: 400 })
    }

    // Check if filter exists and user has permission to delete
    const existingFilter = await prisma.leadSavedFilter.findFirst({
      where: {
        id: filterId,
        companyId: session.user.companyId,
        createdById: session.user.id // Only creator can delete
      }
    })

    if (!existingFilter) {
      return NextResponse.json({ 
        error: 'Filter not found or you do not have permission to delete it' 
      }, { status: 404 })
    }

    await prisma.leadSavedFilter.delete({
      where: { id: filterId }
    })

    return NextResponse.json({ message: 'Filter deleted successfully' })

  } catch (error) {
    console.error('Error deleting saved filter:', error)
    return NextResponse.json(
      { error: 'Failed to delete saved filter' },
      { status: 500 }
    )
  }
}
