"use strict";(()=>{var e={};e.id=3101,e.ids=[3101],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},15254:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>f,originalPathname:()=>h,patchFetch:()=>E,requestAsyncStorage:()=>g,routeModule:()=>I,serverHooks:()=>T,staticGenerationAsyncStorage:()=>A,staticGenerationBailout:()=>w});var n={};a.r(n),a.d(n,{GET:()=>y,POST:()=>b});var r=a(95419),c=a(69108),o=a(99678),i=a(78070),s=a(81355),u=a(3205),l=a(9108),d=a(25252),p=a(52178);let m=d.Ry({name:d.Z_().min(1,"Account name is required"),accountNumber:d.Z_().optional(),accountType:d.Km(["ASSET","LIABILITY","EQUITY","REVENUE","EXPENSE"]),accountSubType:d.Z_().optional(),description:d.Z_().optional(),balance:d.Rx().default(0),currency:d.Z_().default("USD"),isActive:d.O7().default(!0),bankName:d.Z_().optional(),routingNumber:d.Z_().optional(),accountNumberMask:d.Z_().optional(),parentAccountId:d.Z_().optional(),normalBalance:d.Km(["DEBIT","CREDIT"]).default("DEBIT"),taxReportingCode:d.Z_().optional(),isSystemAccount:d.O7().default(!1)});async function y(e){try{let t=await (0,s.getServerSession)(u.L);if(!t?.user?.id||!t?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),n=parseInt(a.get("page")||"1"),r=parseInt(a.get("limit")||"50"),c=a.get("search")||"",o=a.get("accountType")||"",d=a.get("status")||"",p=(n-1)*r,m={companyId:t.user.companyId};c&&(m.OR=[{name:{contains:c,mode:"insensitive"}},{description:{contains:c,mode:"insensitive"}},{accountNumber:{contains:c,mode:"insensitive"}},{accountSubType:{contains:c,mode:"insensitive"}}]),o&&(m.accountType=o),"active"===d?m.isActive=!0:"inactive"===d&&(m.isActive=!1);let[y,b]=await Promise.all([l._.financialAccount.findMany({where:m,include:{parentAccount:{select:{id:!0,name:!0,accountType:!0}},childAccounts:{select:{id:!0,name:!0,accountType:!0,balance:!0}},createdBy:{select:{name:!0,email:!0}},_count:{select:{debitTransactions:!0,creditTransactions:!0,budgets:!0}}},orderBy:[{accountType:"asc"},{name:"asc"}],skip:p,take:r}),l._.financialAccount.count({where:m})]),I=await Promise.all(y.map(async e=>{let t=await l._.accountTransaction.findMany({where:{OR:[{debitAccountId:e.id},{creditAccountId:e.id}]},select:{id:!0,transactionNumber:!0,description:!0,amount:!0,transactionDate:!0,transactionType:!0,status:!0},orderBy:{transactionDate:"desc"},take:5});return{id:e.id,name:e.name,accountNumber:e.accountNumber,accountType:e.accountType,accountSubType:e.accountSubType,description:e.description,balance:Number(e.balance),currency:e.currency,isActive:e.isActive,bankName:e.bankName,routingNumber:e.routingNumber,accountNumberMask:e.accountNumberMask,parentAccount:e.parentAccount,childAccounts:e.childAccounts.map(e=>({...e,balance:Number(e.balance)})),normalBalance:e.normalBalance,taxReportingCode:e.taxReportingCode,isSystemAccount:e.isSystemAccount,createdBy:e.createdBy,createdAt:e.createdAt,updatedAt:e.updatedAt,transactionCount:e._count.debitTransactions+e._count.creditTransactions,budgetCount:e._count.budgets,recentTransactions:t.map(e=>({...e,amount:Number(e.amount)}))}}));return i.Z.json({accounts:I,pagination:{page:n,limit:r,total:b,pages:Math.ceil(b/r)}})}catch(e){return console.error("Error fetching accounts:",e),i.Z.json({error:"Failed to fetch accounts"},{status:500})}}async function b(e){try{let t=await (0,s.getServerSession)(u.L);if(!t?.user?.id||!t?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let a=await e.json(),n=m.parse(a);if(n.accountNumber&&await l._.financialAccount.findFirst({where:{accountNumber:n.accountNumber,companyId:t.user.companyId}}))return i.Z.json({error:"An account with this account number already exists"},{status:400});if(n.parentAccountId){let e=await l._.financialAccount.findFirst({where:{id:n.parentAccountId,companyId:t.user.companyId}});if(!e)return i.Z.json({error:"Parent account not found"},{status:400});if(e.accountType!==n.accountType)return i.Z.json({error:"Parent account must be of the same account type"},{status:400})}if(!n.normalBalance)switch(n.accountType){case"ASSET":case"EXPENSE":n.normalBalance="DEBIT";break;case"LIABILITY":case"EQUITY":case"REVENUE":n.normalBalance="CREDIT"}let r=await l._.$transaction(async e=>{let a=await e.financialAccount.create({data:{...n,companyId:t.user.companyId,createdById:t.user.id},include:{parentAccount:{select:{id:!0,name:!0,accountType:!0}},createdBy:{select:{name:!0,email:!0}}}});return await e.activity.create({data:{type:"SYSTEM",title:"Financial Account Created",description:`Financial account "${n.name}" was created`,companyId:t.user.companyId,createdById:t.user.id}}),a});return i.Z.json({account:{id:r.id,name:r.name,accountNumber:r.accountNumber,accountType:r.accountType,accountSubType:r.accountSubType,description:r.description,balance:Number(r.balance),currency:r.currency,isActive:r.isActive,bankName:r.bankName,routingNumber:r.routingNumber,accountNumberMask:r.accountNumberMask,parentAccount:r.parentAccount,normalBalance:r.normalBalance,taxReportingCode:r.taxReportingCode,isSystemAccount:r.isSystemAccount,createdBy:r.createdBy,createdAt:r.createdAt,updatedAt:r.updatedAt,transactionCount:0,budgetCount:0,recentTransactions:[]},message:"Financial account created successfully"})}catch(e){if(e instanceof p.jm)return i.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error creating account:",e),i.Z.json({error:"Failed to create account"},{status:500})}}let I=new r.AppRouteRouteModule({definition:{kind:c.x.APP_ROUTE,page:"/api/accounts/route",pathname:"/api/accounts",filename:"route",bundlePath:"app/api/accounts/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\accounts\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:g,staticGenerationAsyncStorage:A,serverHooks:T,headerHooks:f,staticGenerationBailout:w}=I,h="/api/accounts/route";function E(){return(0,o.patchFetch)({serverHooks:T,staticGenerationAsyncStorage:A})}},3205:(e,t,a)=>{a.d(t,{L:()=>u});var n=a(86485),r=a(10375),c=a(50694),o=a(6521),i=a.n(o),s=a(9108);let u={providers:[(0,n.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await s._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await s._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await s._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await s._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,r.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,c.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>r});let n=require("@prisma/client"),r=globalThis.prisma??new n.PrismaClient}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),n=t.X(0,[1638,6206,6521,2455,4520,5252],()=>a(15254));module.exports=n})();