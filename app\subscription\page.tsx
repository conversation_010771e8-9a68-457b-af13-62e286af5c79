'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  CreditCard, 
  Calendar, 
  Users, 
  Database, 
  FileText, 
  Receipt, 
  FileContract,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react'
import { toast } from 'sonner'

interface SubscriptionData {
  hasActiveSubscription: boolean
  subscription: any
  usage: any
}

export default function SubscriptionPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (session?.user) {
      fetchSubscriptionData()
    }
  }, [session])

  const fetchSubscriptionData = async () => {
    try {
      const response = await fetch('/api/subscription')
      const data = await response.json()

      if (data.success) {
        setSubscriptionData(data.data)
      } else {
        toast.error('Failed to load subscription data')
      }
    } catch (error) {
      console.error('Error fetching subscription:', error)
      toast.error('Failed to load subscription data')
    } finally {
      setLoading(false)
    }
  }

  const handleBillingPortal = async () => {
    try {
      const response = await fetch('/api/subscription/portal', {
        method: 'POST'
      })
      const data = await response.json()

      if (data.success) {
        window.location.href = data.data.url
      } else {
        toast.error(data.error || 'Failed to open billing portal')
      }
    } catch (error) {
      console.error('Error opening billing portal:', error)
      toast.error('Failed to open billing portal')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800'
      case 'TRIALING':
        return 'bg-blue-100 text-blue-800'
      case 'PAST_DUE':
        return 'bg-yellow-100 text-yellow-800'
      case 'CANCELED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500'
    if (percentage >= 75) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  if (!subscriptionData?.hasActiveSubscription) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <CreditCard className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">No Active Subscription</h2>
          <p className="text-gray-600 mb-6">
            You don't have an active subscription. Choose a plan to get started.
          </p>
          <Button onClick={() => router.push('/pricing')}>
            View Pricing Plans
          </Button>
        </div>
      </div>
    )
  }

  const { subscription, usage } = subscriptionData

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Subscription</h1>
          <p className="text-gray-600">Manage your subscription and view usage</p>
        </div>
        <Button variant="outline" onClick={() => router.push('/pricing')}>
          Change Plan
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="usage">Usage</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Current Plan */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    Current Plan
                  </CardTitle>
                  <CardDescription>Your active subscription details</CardDescription>
                </div>
                <Badge className={getStatusColor(subscription.status)}>
                  {subscription.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-xl font-semibold">{subscription.pricingPlan.name}</h3>
                  <p className="text-gray-600">{subscription.pricingPlan.description}</p>
                  <div className="mt-2">
                    <span className="text-2xl font-bold">
                      ${subscription.billingCycle === 'YEARLY' 
                        ? (subscription.pricingPlan.yearlyPrice / 12).toFixed(0)
                        : subscription.pricingPlan.monthlyPrice
                      }
                    </span>
                    <span className="text-gray-500">/month</span>
                    {subscription.billingCycle === 'YEARLY' && (
                      <Badge variant="secondary" className="ml-2">
                        Billed Yearly
                      </Badge>
                    )}
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="flex items-center text-sm text-gray-600">
                      <Calendar className="h-4 w-4 mr-2" />
                      {subscription.status === 'TRIALING' ? 'Trial ends' : 'Next billing'}
                    </span>
                    <span className="font-medium">
                      {subscription.daysUntilRenewal > 0 
                        ? `${subscription.daysUntilRenewal} days`
                        : 'Today'
                      }
                    </span>
                  </div>
                  
                  {subscription.status === 'TRIALING' && (
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center text-blue-700">
                        <Clock className="h-4 w-4 mr-2" />
                        <span className="text-sm font-medium">
                          Free trial active
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Usage Overview */}
          <div className="grid md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Users</p>
                    <p className="text-2xl font-bold">
                      {usage.users.current}/{usage.users.limit}
                    </p>
                  </div>
                  <Users className="h-8 w-8 text-blue-500" />
                </div>
                <Progress 
                  value={usage.users.percentage} 
                  className="mt-3"
                  style={{ '--progress-background': getUsageColor(usage.users.percentage) } as any}
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Storage</p>
                    <p className="text-2xl font-bold">{usage.storage.currentFormatted}</p>
                    <p className="text-xs text-gray-500">of {usage.storage.limitFormatted}</p>
                  </div>
                  <Database className="h-8 w-8 text-green-500" />
                </div>
                <Progress 
                  value={usage.storage.percentage} 
                  className="mt-3"
                  style={{ '--progress-background': getUsageColor(usage.storage.percentage) } as any}
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Customers</p>
                    <p className="text-2xl font-bold">
                      {usage.customers.current}/{usage.customers.limit}
                    </p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-purple-500" />
                </div>
                <Progress 
                  value={usage.customers.percentage} 
                  className="mt-3"
                  style={{ '--progress-background': getUsageColor(usage.customers.percentage) } as any}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="usage" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Usage Details</CardTitle>
              <CardDescription>
                Monitor your usage across all plan limits
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {[
                  { key: 'users', label: 'Users', icon: Users },
                  { key: 'customers', label: 'Customers', icon: TrendingUp },
                  { key: 'quotations', label: 'Quotations', icon: FileText },
                  { key: 'invoices', label: 'Invoices', icon: Receipt },
                  { key: 'contracts', label: 'Contracts', icon: FileContract },
                  { key: 'storage', label: 'Storage', icon: Database }
                ].map(({ key, label, icon: Icon }) => {
                  const usageItem = usage[key]
                  const isNearLimit = usageItem.percentage >= 80
                  
                  return (
                    <div key={key} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Icon className="h-4 w-4 text-gray-500" />
                          <span className="font-medium">{label}</span>
                          {isNearLimit && (
                            <AlertTriangle className="h-4 w-4 text-yellow-500" />
                          )}
                        </div>
                        <span className="text-sm text-gray-600">
                          {key === 'storage' 
                            ? `${usageItem.currentFormatted} / ${usageItem.limitFormatted}`
                            : `${usageItem.current} / ${usageItem.limit}`
                          }
                        </span>
                      </div>
                      <Progress 
                        value={usageItem.percentage} 
                        className={`h-2 ${isNearLimit ? 'bg-yellow-100' : ''}`}
                        style={{ '--progress-background': getUsageColor(usageItem.percentage) } as any}
                      />
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>{usageItem.percentage}% used</span>
                        {isNearLimit && (
                          <span className="text-yellow-600 font-medium">
                            Approaching limit
                          </span>
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="billing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Billing Information</CardTitle>
              <CardDescription>
                Manage your billing details and payment methods
              </CardDescription>
            </CardHeader>
            <CardContent>
              {subscription.stripeCustomerId ? (
                <div className="space-y-6">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h4 className="font-medium">Payment Methods</h4>
                      <p className="text-sm text-gray-600">
                        Manage your payment methods and billing information
                      </p>
                    </div>
                    <Button onClick={handleBillingPortal}>
                      <CreditCard className="h-4 w-4 mr-2" />
                      Manage Billing
                    </Button>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium mb-2">Current Plan</h4>
                      <p className="text-2xl font-bold">
                        ${subscription.billingCycle === 'YEARLY'
                          ? (subscription.pricingPlan.yearlyPrice / 12).toFixed(0)
                          : subscription.pricingPlan.monthlyPrice
                        }
                      </p>
                      <p className="text-sm text-gray-600">
                        per month {subscription.billingCycle === 'YEARLY' && '(billed annually)'}
                      </p>
                    </div>

                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium mb-2">Next Payment</h4>
                      <p className="text-lg font-semibold">
                        {subscription.daysUntilRenewal > 0
                          ? `${subscription.daysUntilRenewal} days`
                          : 'Today'
                        }
                      </p>
                      <p className="text-sm text-gray-600">
                        {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No Payment Method Required
                  </h3>
                  <p className="text-gray-600 mb-4">
                    You're currently on a free plan that doesn't require payment.
                  </p>
                  <Button variant="outline" onClick={() => router.push('/pricing')}>
                    Upgrade Plan
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
