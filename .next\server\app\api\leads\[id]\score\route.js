"use strict";(()=>{var e={};e.id=9485,e.ids=[9485],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},61551:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>b,originalPathname:()=>A,patchFetch:()=>E,requestAsyncStorage:()=>f,routeModule:()=>I,serverHooks:()=>v,staticGenerationAsyncStorage:()=>w,staticGenerationBailout:()=>T});var i={};a.r(i),a.d(i,{GET:()=>h,PUT:()=>g});var r=a(95419),n=a(69108),s=a(99678),o=a(78070),c=a(81355),d=a(3205),l=a(9108),u=a(25252),p=a(52178);let m={companySize:{STARTUP:5,SMALL:10,MEDIUM:20,LARGE:25,ENTERPRISE:30},industry:{TECHNOLOGY:25,FINANCE:20,HEALTHCARE:20,MANUFACTURING:15,RETAIL:10,OTHER:5},hasPhone:10,hasWebsite:5,hasCompanyEmail:5,activityCount:{0:0,1:5,2:10,3:15,4:20,5:25},recentActivity:10,hasBudget:15,hasTimeline:10},y=u.Ry({manualScore:u.Rx().min(0).max(100).optional(),reason:u.Z_().optional(),qualificationNotes:u.Z_().optional()});async function h(e,{params:t}){try{var a;let e=await (0,c.getServerSession)(d.L);if(!e?.user?.id||!e?.user?.companyId)return o.Z.json({error:"Unauthorized"},{status:401});let i=await l._.lead.findFirst({where:{id:t.id,companyId:e.user.companyId},include:{activities:{orderBy:{createdAt:"desc"},take:10},_count:{select:{activities:!0,leadNotes:!0}}}});if(!i)return o.Z.json({error:"Lead not found"},{status:404});let r=function(e){let t=0;e.companySize&&m.companySize[e.companySize]&&(t+=m.companySize[e.companySize]),e.industry&&m.industry[e.industry]&&(t+=m.industry[e.industry]),e.phone&&(t+=m.hasPhone),e.website&&(t+=m.hasWebsite),e.email&&e.companyName&&e.email.includes(e.companyName.toLowerCase())&&(t+=m.hasCompanyEmail);let a=Math.min(e._count.activities,5);if(t+=m.activityCount[a]||0,e.activities.length>0){let a=new Date(e.activities[0].createdAt);(Date.now()-a.getTime())/864e5<=7&&(t+=m.recentActivity)}return e.budget&&e.budget>0&&(t+=m.hasBudget),e.timeline&&(t+=m.hasTimeline),Math.min(t,100)}(i),n=await l._.leadScoreHistory.findMany({where:{leadId:t.id},orderBy:{createdAt:"desc"},take:20,include:{createdBy:{select:{id:!0,name:!0,email:!0}}}}),s=(a=i.score)>=70?"HOT":a>=40?"WARM":"COLD",u=function(e){let t={hasContactInfo:!!(e.phone||e.email),hasCompanyInfo:!!e.companyName,hasBudget:!!(e.budget&&e.budget>0),hasTimeline:!!e.timeline,hasEngagement:e._count.activities>0},a=Object.values(t).filter(Boolean).length;return{isQualified:a>=3,qualificationScore:Math.round(a/Object.keys(t).length*100),criteria:t,missingCriteria:Object.entries(t).filter(([e,t])=>!t).map(([e])=>e)}}(i);return o.Z.json({leadId:t.id,currentScore:i.score,automatedScore:r,temperature:s,qualificationStatus:u,scoreBreakdown:function(e){let t=[];e.companySize&&t.push({category:"Company Size",points:m.companySize[e.companySize]||0,maxPoints:30,description:`${e.companySize} company`}),e.industry&&t.push({category:"Industry",points:m.industry[e.industry]||0,maxPoints:25,description:`${e.industry} industry`});let a=0;e.phone&&(a+=m.hasPhone),e.website&&(a+=m.hasWebsite),e.email&&e.companyName&&e.email.includes(e.companyName.toLowerCase())&&(a+=m.hasCompanyEmail),t.push({category:"Contact Information",points:a,maxPoints:20,description:"Phone, website, company email"});let i=Math.min(e._count.activities,5),r=m.activityCount[i]||0;if(e.activities.length>0){let t=new Date(e.activities[0].createdAt);(Date.now()-t.getTime())/864e5<=7&&(r+=m.recentActivity)}t.push({category:"Engagement",points:r,maxPoints:25,description:`${e._count.activities} activities, recent engagement`});let n=0;return e.budget&&e.budget>0&&(n+=m.hasBudget),e.timeline&&(n+=m.hasTimeline),t.push({category:"Budget & Timeline",points:n,maxPoints:25,description:"Budget and timeline information"}),t}(i),scoreHistory:n,recommendations:function(e,t){let a=[];return e.phone||a.push({type:"CONTACT_INFO",priority:"HIGH",message:"Add phone number to increase lead score by 10 points",action:"Add phone number"}),e.website||a.push({type:"CONTACT_INFO",priority:"MEDIUM",message:"Add company website to increase lead score by 5 points",action:"Add website"}),e.budget&&0!==e.budget||a.push({type:"QUALIFICATION",priority:"HIGH",message:"Qualify budget to increase lead score by 15 points",action:"Qualify budget"}),e.timeline||a.push({type:"QUALIFICATION",priority:"HIGH",message:"Determine timeline to increase lead score by 10 points",action:"Determine timeline"}),0===e._count.activities&&a.push({type:"ENGAGEMENT",priority:"URGENT",message:"No activities recorded. Schedule a call or meeting.",action:"Schedule activity"}),a}(i,0)})}catch(e){return console.error("Error fetching lead score:",e),o.Z.json({error:"Failed to fetch lead score"},{status:500})}}async function g(e,{params:t}){try{let a=await (0,c.getServerSession)(d.L);if(!a?.user?.id||!a?.user?.companyId)return o.Z.json({error:"Unauthorized"},{status:401});let i=await e.json(),r=y.parse(i),n=await l._.lead.findFirst({where:{id:t.id,companyId:a.user.companyId}});if(!n)return o.Z.json({error:"Lead not found"},{status:404});let s=n.score;return void 0!==r.manualScore&&(s=r.manualScore,await l._.leadScoreHistory.create({data:{leadId:t.id,previousScore:n.score,newScore:s,changeReason:r.reason||"Manual score update",isManual:!0,createdById:a.user.id,companyId:a.user.companyId}}),await l._.lead.update({where:{id:t.id},data:{score:s,updatedAt:new Date}})),r.qualificationNotes&&await l._.leadNote.create({data:{leadId:t.id,title:"Qualification Notes",content:r.qualificationNotes,isPrivate:!1,createdById:a.user.id,companyId:a.user.companyId}}),o.Z.json({message:"Lead score updated successfully",newScore:s})}catch(e){if(e instanceof p.jm)return o.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error updating lead score:",e),o.Z.json({error:"Failed to update lead score"},{status:500})}}let I=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/leads/[id]/score/route",pathname:"/api/leads/[id]/score",filename:"route",bundlePath:"app/api/leads/[id]/score/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\leads\\[id]\\score\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:f,staticGenerationAsyncStorage:w,serverHooks:v,headerHooks:b,staticGenerationBailout:T}=I,A="/api/leads/[id]/score/route";function E(){return(0,s.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:w})}},3205:(e,t,a)=>{a.d(t,{L:()=>d});var i=a(86485),r=a(10375),n=a(50694),s=a(6521),o=a.n(s),c=a(9108);let d={providers:[(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await c._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await c._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,r.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>r});let i=require("@prisma/client"),r=globalThis.prisma??new i.PrismaClient}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[1638,6206,6521,2455,4520,5252],()=>a(61551));module.exports=i})();