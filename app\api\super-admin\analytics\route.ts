import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // days
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(period))

    // Get comprehensive super admin analytics
    const [
      systemOverview,
      companyMetrics,
      userMetrics,
      activityMetrics,
      performanceMetrics,
      securityMetrics,
      revenueMetrics,
      growthMetrics,
      topCompanies,
      recentActivities,
      systemHealth,
      featureUsage
    ] = await Promise.all([
      // System overview
      Promise.all([
        prisma.company.count(),
        prisma.user.count(),
        prisma.quotation.count(),
        prisma.invoice.count(),
        prisma.contract.count(),
        prisma.item.count()
      ]),

      // Company metrics
      Promise.all([
        // Active companies
        prisma.company.count({
          where: {
            status: 'ACTIVE'
          }
        }),
        // New companies this period
        prisma.company.count({
          where: {
            createdAt: { gte: startDate }
          }
        }),
        // Companies by size
        prisma.company.groupBy({
          by: ['size'],
          _count: {
            id: true
          }
        }),
        // Companies by industry
        prisma.company.groupBy({
          by: ['industry'],
          _count: {
            id: true
          },
          orderBy: {
            _count: {
              id: 'desc'
            }
          },
          take: 10
        })
      ]),

      // User metrics
      Promise.all([
        // Active users
        prisma.user.count({
          where: {
            status: 'ACTIVE'
          }
        }),
        // New users this period
        prisma.user.count({
          where: {
            createdAt: { gte: startDate }
          }
        }),
        // Users by role
        prisma.user.groupBy({
          by: ['role'],
          _count: {
            id: true
          }
        }),
        // User login activity
        prisma.user.aggregate({
          _avg: {
            loginCount: true
          },
          _sum: {
            loginCount: true
          }
        })
      ]),

      // Activity metrics
      Promise.all([
        // Total activities
        prisma.activity.count({
          where: {
            createdAt: { gte: startDate }
          }
        }),
        // Activities by type
        prisma.activity.groupBy({
          by: ['type'],
          where: {
            createdAt: { gte: startDate }
          },
          _count: {
            id: true
          },
          orderBy: {
            _count: {
              id: 'desc'
            }
          }
        }),
        // Daily activity trend
        prisma.$queryRaw`
          SELECT 
            DATE(created_at) as date,
            COUNT(*) as count
          FROM activities
          WHERE created_at >= ${startDate}
          GROUP BY DATE(created_at)
          ORDER BY date DESC
          LIMIT 30
        `
      ]),

      // Performance metrics
      Promise.all([
        // Document creation rates
        prisma.quotation.count({
          where: {
            createdAt: { gte: startDate }
          }
        }),
        prisma.invoice.count({
          where: {
            createdAt: { gte: startDate }
          }
        }),
        prisma.contract.count({
          where: {
            createdAt: { gte: startDate }
          }
        }),
        // Average documents per company
        prisma.$queryRaw`
          SELECT 
            AVG(doc_count) as avg_documents_per_company
          FROM (
            SELECT 
              company_id,
              (
                (SELECT COUNT(*) FROM quotations WHERE company_id = c.id) +
                (SELECT COUNT(*) FROM invoices WHERE company_id = c.id) +
                (SELECT COUNT(*) FROM contracts WHERE company_id = c.id)
              ) as doc_count
            FROM companies c
            WHERE c.status = 'ACTIVE'
          ) as company_docs
        `
      ]),

      // Security metrics
      Promise.all([
        // Failed login attempts (if tracked)
        prisma.user.count({
          where: {
            status: 'SUSPENDED'
          }
        }),
        // Companies with security issues
        prisma.company.count({
          where: {
            status: 'SUSPENDED'
          }
        }),
        // Recent security activities
        prisma.activity.count({
          where: {
            type: 'SECURITY',
            createdAt: { gte: startDate }
          }
        })
      ]),

      // Revenue metrics (placeholder - would integrate with billing system)
      Promise.all([
        // Total revenue calculation (placeholder)
        prisma.invoice.aggregate({
          where: {
            status: 'PAID',
            createdAt: { gte: startDate }
          },
          _sum: {
            totalAmount: true
          }
        }),
        // Average revenue per company
        prisma.$queryRaw`
          SELECT 
            AVG(total_revenue) as avg_revenue_per_company
          FROM (
            SELECT 
              company_id,
              SUM(total_amount) as total_revenue
            FROM invoices
            WHERE status = 'PAID' AND created_at >= ${startDate}
            GROUP BY company_id
          ) as company_revenue
        `
      ]),

      // Growth metrics
      Promise.all([
        // Month-over-month growth
        prisma.$queryRaw`
          SELECT 
            DATE_TRUNC('month', created_at) as month,
            COUNT(*) as companies_count
          FROM companies
          WHERE created_at >= ${new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)}
          GROUP BY DATE_TRUNC('month', created_at)
          ORDER BY month DESC
          LIMIT 12
        `,
        // User growth
        prisma.$queryRaw`
          SELECT 
            DATE_TRUNC('month', created_at) as month,
            COUNT(*) as users_count
          FROM users
          WHERE created_at >= ${new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)}
          GROUP BY DATE_TRUNC('month', created_at)
          ORDER BY month DESC
          LIMIT 12
        `
      ]),

      // Top performing companies
      prisma.$queryRaw`
        SELECT 
          c.id,
          c.name,
          c.industry,
          c.size,
          COUNT(DISTINCT u.id) as user_count,
          COUNT(DISTINCT q.id) as quotation_count,
          COUNT(DISTINCT i.id) as invoice_count,
          COUNT(DISTINCT ct.id) as contract_count,
          COALESCE(SUM(i.total_amount), 0) as total_revenue
        FROM companies c
        LEFT JOIN users u ON c.id = u.company_id
        LEFT JOIN quotations q ON c.id = q.company_id
        LEFT JOIN invoices i ON c.id = i.company_id AND i.status = 'PAID'
        LEFT JOIN contracts ct ON c.id = ct.company_id
        WHERE c.status = 'ACTIVE'
        GROUP BY c.id, c.name, c.industry, c.size
        ORDER BY total_revenue DESC, user_count DESC
        LIMIT 10
      `,

      // Recent activities across all companies
      prisma.activity.findMany({
        where: {
          createdAt: { gte: startDate }
        },
        include: {
          company: {
            select: {
              name: true
            }
          },
          createdBy: {
            select: {
              name: true,
              email: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 20
      }),

      // System health metrics
      Promise.all([
        // Database size metrics (placeholder)
        prisma.company.count(),
        prisma.user.count(),
        prisma.activity.count(),
        // Error rates (would need error tracking)
        prisma.activity.count({
          where: {
            type: 'ERROR',
            createdAt: { gte: startDate }
          }
        })
      ]),

      // Feature usage across companies
      Promise.all([
        // Companies using quotations
        prisma.company.count({
          where: {
            quotations: {
              some: {}
            }
          }
        }),
        // Companies using invoices
        prisma.company.count({
          where: {
            invoices: {
              some: {}
            }
          }
        }),
        // Companies using contracts
        prisma.company.count({
          where: {
            contracts: {
              some: {}
            }
          }
        }),
        // Companies using items
        prisma.company.count({
          where: {
            items: {
              some: {}
            }
          }
        })
      ])
    ])

    // Process metrics
    const [totalCompanies, totalUsers, totalQuotations, totalInvoices, totalContracts, totalItems] = systemOverview
    const [activeCompanies, newCompanies, companiesBySize, companiesByIndustry] = companyMetrics
    const [activeUsers, newUsers, usersByRole, userLoginStats] = userMetrics
    const [totalActivities, activitiesByType, dailyActivityTrend] = activityMetrics
    const [newQuotations, newInvoices, newContracts, avgDocsPerCompany] = performanceMetrics
    const [suspendedUsers, suspendedCompanies, securityActivities] = securityMetrics
    const [totalRevenue, avgRevenuePerCompany] = revenueMetrics
    const [companyGrowth, userGrowth] = growthMetrics
    const [totalCompaniesCount, totalUsersCount, totalActivitiesCount, errorCount] = systemHealth
    const [quotationCompanies, invoiceCompanies, contractCompanies, itemCompanies] = featureUsage

    return NextResponse.json({
      summary: {
        totalCompanies,
        totalUsers,
        totalQuotations,
        totalInvoices,
        totalContracts,
        totalItems,
        activeCompanies,
        activeUsers,
        newCompanies,
        newUsers,
        totalActivities,
        totalRevenue: Number(totalRevenue._sum.totalAmount || 0),
        avgRevenuePerCompany: Number((avgRevenuePerCompany as any[])[0]?.avg_revenue_per_company || 0),
        avgDocsPerCompany: Number((avgDocsPerCompany as any[])[0]?.avg_documents_per_company || 0)
      },
      companies: {
        total: totalCompanies,
        active: activeCompanies,
        new: newCompanies,
        bySize: companiesBySize.map(item => ({
          size: item.size,
          count: item._count.id
        })),
        byIndustry: companiesByIndustry.map(item => ({
          industry: item.industry,
          count: item._count.id
        })),
        topPerforming: (topCompanies as any[]).map(company => ({
          id: company.id,
          name: company.name,
          industry: company.industry,
          size: company.size,
          userCount: Number(company.user_count),
          quotationCount: Number(company.quotation_count),
          invoiceCount: Number(company.invoice_count),
          contractCount: Number(company.contract_count),
          totalRevenue: Number(company.total_revenue)
        }))
      },
      users: {
        total: totalUsers,
        active: activeUsers,
        new: newUsers,
        suspended: suspendedUsers,
        byRole: usersByRole.map(item => ({
          role: item.role,
          count: item._count.id
        })),
        avgLoginCount: Number(userLoginStats._avg.loginCount || 0),
        totalLogins: Number(userLoginStats._sum.loginCount || 0)
      },
      activities: {
        total: totalActivities,
        byType: activitiesByType.map(item => ({
          type: item.type,
          count: item._count.id
        })),
        dailyTrend: (dailyActivityTrend as any[]).map(item => ({
          date: item.date,
          count: Number(item.count)
        })),
        recent: recentActivities.map(activity => ({
          id: activity.id,
          type: activity.type,
          title: activity.title,
          description: activity.description,
          company: activity.company?.name,
          createdBy: activity.createdBy?.name,
          createdAt: activity.createdAt
        }))
      },
      performance: {
        newQuotations,
        newInvoices,
        newContracts,
        avgDocsPerCompany: Number((avgDocsPerCompany as any[])[0]?.avg_documents_per_company || 0)
      },
      security: {
        suspendedUsers,
        suspendedCompanies,
        securityActivities,
        errorCount
      },
      growth: {
        companies: (companyGrowth as any[]).map(item => ({
          month: item.month,
          count: Number(item.companies_count)
        })),
        users: (userGrowth as any[]).map(item => ({
          month: item.month,
          count: Number(item.users_count)
        }))
      },
      systemHealth: {
        totalCompanies: totalCompaniesCount,
        totalUsers: totalUsersCount,
        totalActivities: totalActivitiesCount,
        errorRate: totalActivitiesCount > 0 ? (errorCount / totalActivitiesCount) * 100 : 0,
        uptime: 99.9 // Placeholder - would integrate with monitoring
      },
      featureAdoption: {
        quotations: {
          companies: quotationCompanies,
          adoptionRate: totalCompanies > 0 ? (quotationCompanies / totalCompanies) * 100 : 0
        },
        invoices: {
          companies: invoiceCompanies,
          adoptionRate: totalCompanies > 0 ? (invoiceCompanies / totalCompanies) * 100 : 0
        },
        contracts: {
          companies: contractCompanies,
          adoptionRate: totalCompanies > 0 ? (contractCompanies / totalCompanies) * 100 : 0
        },
        items: {
          companies: itemCompanies,
          adoptionRate: totalCompanies > 0 ? (itemCompanies / totalCompanies) * 100 : 0
        }
      },
      period: parseInt(period)
    })

  } catch (error) {
    console.error('Error fetching super admin analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch super admin analytics' },
      { status: 500 }
    )
  }
}
