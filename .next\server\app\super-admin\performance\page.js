(()=>{var e={};e.id=8654,e.ids=[8654],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},62609:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>u,tree:()=>c});var t=a(50482),r=a(69108),n=a(62563),i=a.n(n),l=a(68300),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);a.d(s,d);let c=["",{children:["super-admin",{children:["performance",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,74781)),"C:\\proj\\nextjs-saas\\app\\super-admin\\performance\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,11285)),"C:\\proj\\nextjs-saas\\app\\super-admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\proj\\nextjs-saas\\app\\super-admin\\performance\\page.tsx"],m="/super-admin/performance/page",x={require:a,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/super-admin/performance/page",pathname:"/super-admin/performance",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},86789:(e,s,a)=>{Promise.resolve().then(a.bind(a,82450))},82450:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>k});var t=a(95344),r=a(3729),n=a(61351),i=a(69436),l=a(16212),d=a(76461),c=a(25757),o=a(81036),m=a(7060),x=a(45961),u=a(88534),h=a(46064),p=a(77402),f=a(33733),j=a(50340),v=a(75203),y=a(73577),g=a(73557);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let N=(0,a(69224).Z)("Wifi",[["path",{d:"M5 13a10 10 0 0 1 14 0",key:"6v8j51"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]);var b=a(99046),w=a(30304);function k(){let[e,s]=(0,r.useState)(!0),[a,k]=(0,r.useState)(new Date),[Z]=(0,r.useState)([{id:"1",name:"Response Time",value:245,unit:"ms",status:"good",trend:"down",change:-12},{id:"2",name:"Throughput",value:1250,unit:"req/min",status:"good",trend:"up",change:8},{id:"3",name:"Error Rate",value:.12,unit:"%",status:"good",trend:"down",change:-.05},{id:"4",name:"Database Query Time",value:89,unit:"ms",status:"warning",trend:"up",change:15}]),[P]=(0,r.useState)([{name:"CPU Usage",usage:45,limit:100,status:"good"},{name:"Memory Usage",usage:68,limit:100,status:"warning"},{name:"Disk Usage",usage:32,limit:100,status:"good"},{name:"Network I/O",usage:23,limit:100,status:"good"}]);(0,r.useEffect)(()=>{let e=setTimeout(()=>{s(!1)},1e3);return()=>clearTimeout(e)},[]);let C=e=>{switch(e){case"good":return t.jsx(m.Z,{className:"h-4 w-4 text-green-500"});case"warning":return t.jsx(x.Z,{className:"h-4 w-4 text-yellow-500"});case"critical":return t.jsx(x.Z,{className:"h-4 w-4 text-red-500"});default:return t.jsx(u.Z,{className:"h-4 w-4 text-gray-500"})}},R=e=>{switch(e){case"up":return t.jsx(h.Z,{className:"h-4 w-4 text-green-500"});case"down":return t.jsx(p.Z,{className:"h-4 w-4 text-red-500"});default:return t.jsx(u.Z,{className:"h-4 w-4 text-gray-500"})}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Performance Monitor"}),t.jsx("p",{className:"text-muted-foreground",children:"Real-time system performance metrics and monitoring"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(i.C,{variant:"outline",className:"text-xs",children:["Last updated: ",a.toLocaleTimeString()]}),(0,t.jsxs)(l.z,{onClick:()=>{s(!0),k(new Date),setTimeout(()=>s(!1),1e3)},disabled:e,size:"sm",children:[t.jsx(f.Z,{className:`h-4 w-4 mr-2 ${e?"animate-spin":""}`}),"Refresh"]})]})]}),t.jsx("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:Z.map(e=>(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(n.ll,{className:"text-sm font-medium",children:e.name}),C(e.status)]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[e.value,e.unit]}),(0,t.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[R(e.trend),(0,t.jsxs)("span",{className:"ml-1",children:[e.change>0?"+":"",e.change,"% from last hour"]})]})]})]},e.id))}),(0,t.jsxs)(c.mQ,{defaultValue:"overview",className:"space-y-4",children:[(0,t.jsxs)(c.dr,{children:[t.jsx(c.SP,{value:"overview",children:"Overview"}),t.jsx(c.SP,{value:"resources",children:"System Resources"}),t.jsx(c.SP,{value:"database",children:"Database"}),t.jsx(c.SP,{value:"network",children:"Network"})]}),t.jsx(c.nU,{value:"overview",className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsxs)(n.ll,{className:"flex items-center",children:[t.jsx(j.Z,{className:"h-5 w-5 mr-2"}),"Performance Trends"]}),t.jsx(n.SZ,{children:"Key metrics over the last 24 hours"})]}),t.jsx(n.aY,{children:t.jsx("div",{className:"space-y-4",children:Z.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[C(e.status),t.jsx("span",{className:"text-sm font-medium",children:e.name})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"text-sm",children:[e.value,e.unit]}),R(e.trend)]})]},e.id))})})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsxs)(n.ll,{className:"flex items-center",children:[t.jsx(v.Z,{className:"h-5 w-5 mr-2"}),"System Health"]}),t.jsx(n.SZ,{children:"Current system resource utilization"})]}),t.jsx(n.aY,{children:t.jsx("div",{className:"space-y-4",children:P.map(e=>(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[t.jsx("span",{className:"font-medium",children:e.name}),(0,t.jsxs)("span",{children:[e.usage,"%"]})]}),t.jsx(d.E,{value:e.usage,className:"h-2"})]},e.name))})})]})]})}),t.jsx(c.nU,{value:"resources",className:"space-y-4",children:(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[t.jsx(n.ll,{children:"System Resources"}),t.jsx(n.SZ,{children:"Detailed view of system resource utilization"})]}),t.jsx(n.aY,{children:(0,t.jsxs)(o.iA,{children:[t.jsx(o.xD,{children:(0,t.jsxs)(o.SC,{children:[t.jsx(o.ss,{children:"Resource"}),t.jsx(o.ss,{children:"Usage"}),t.jsx(o.ss,{children:"Status"}),t.jsx(o.ss,{children:"Trend"})]})}),t.jsx(o.RM,{children:P.map(e=>(0,t.jsxs)(o.SC,{children:[t.jsx(o.pj,{className:"font-medium",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:["CPU Usage"===e.name&&t.jsx(y.Z,{className:"h-4 w-4"}),"Memory Usage"===e.name&&t.jsx(v.Z,{className:"h-4 w-4"}),"Disk Usage"===e.name&&t.jsx(g.Z,{className:"h-4 w-4"}),"Network I/O"===e.name&&t.jsx(N,{className:"h-4 w-4"}),t.jsx("span",{children:e.name})]})}),t.jsx(o.pj,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(d.E,{value:e.usage,className:"w-20 h-2"}),(0,t.jsxs)("span",{className:"text-sm",children:[e.usage,"%"]})]})}),t.jsx(o.pj,{children:t.jsx(i.C,{variant:"good"===e.status?"default":"destructive",className:"capitalize",children:e.status})}),t.jsx(o.pj,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[t.jsx(h.Z,{className:"h-4 w-4 text-green-500"}),t.jsx("span",{className:"text-sm text-muted-foreground",children:"Stable"})]})})]},e.name))})]})})]})}),t.jsx(c.nU,{value:"database",className:"space-y-4",children:(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsxs)(n.ll,{className:"flex items-center",children:[t.jsx(b.Z,{className:"h-5 w-5 mr-2"}),"Database Performance"]}),t.jsx(n.SZ,{children:"Database query performance and connection metrics"})]}),t.jsx(n.aY,{children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("div",{className:"text-sm font-medium",children:"Query Performance"}),t.jsx("div",{className:"text-2xl font-bold",children:"89ms"}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Average query time"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("div",{className:"text-sm font-medium",children:"Active Connections"}),t.jsx("div",{className:"text-2xl font-bold",children:"24"}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Current connections"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("div",{className:"text-sm font-medium",children:"Cache Hit Rate"}),t.jsx("div",{className:"text-2xl font-bold",children:"94.2%"}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Query cache efficiency"})]})]})})]})}),t.jsx(c.nU,{value:"network",className:"space-y-4",children:(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsxs)(n.ll,{className:"flex items-center",children:[t.jsx(w.Z,{className:"h-5 w-5 mr-2"}),"Network Performance"]}),t.jsx(n.SZ,{children:"Network latency and bandwidth utilization"})]}),t.jsx(n.aY,{children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("div",{className:"text-sm font-medium",children:"Latency"}),t.jsx("div",{className:"text-2xl font-bold",children:"12ms"}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Average response time"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("div",{className:"text-sm font-medium",children:"Bandwidth Usage"}),t.jsx("div",{className:"text-2xl font-bold",children:"23%"}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Current utilization"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("div",{className:"text-sm font-medium",children:"Packet Loss"}),t.jsx("div",{className:"text-2xl font-bold",children:"0.01%"}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Network reliability"})]})]})})]})})]})]})}},76461:(e,s,a)=>{"use strict";a.d(s,{E:()=>N});var t=a(95344),r=a(3729),n=a(98462),i=a(62409),l="Progress",[d,c]=(0,n.b)(l),[o,m]=d(l),x=r.forwardRef((e,s)=>{var a,r;let{__scopeProgress:n,value:l=null,max:d,getValueLabel:c=p,...m}=e;(d||0===d)&&!v(d)&&console.error((a=`${d}`,`Invalid prop \`max\` of value \`${a}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let x=v(d)?d:100;null===l||y(l,x)||console.error((r=`${l}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let u=y(l,x)?l:null,h=j(u)?c(u,x):void 0;return(0,t.jsx)(o,{scope:n,value:u,max:x,children:(0,t.jsx)(i.WV.div,{"aria-valuemax":x,"aria-valuemin":0,"aria-valuenow":j(u)?u:void 0,"aria-valuetext":h,role:"progressbar","data-state":f(u,x),"data-value":u??void 0,"data-max":x,...m,ref:s})})});x.displayName=l;var u="ProgressIndicator",h=r.forwardRef((e,s)=>{let{__scopeProgress:a,...r}=e,n=m(u,a);return(0,t.jsx)(i.WV.div,{"data-state":f(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...r,ref:s})});function p(e,s){return`${Math.round(e/s*100)}%`}function f(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function j(e){return"number"==typeof e}function v(e){return j(e)&&!isNaN(e)&&e>0}function y(e,s){return j(e)&&!isNaN(e)&&e<=s&&e>=0}h.displayName=u;var g=a(91626);let N=r.forwardRef(({className:e,value:s,...a},r)=>t.jsx(x,{ref:r,className:(0,g.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...a,children:t.jsx(h,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})}));N.displayName=x.displayName},81036:(e,s,a)=>{"use strict";a.d(s,{RM:()=>d,SC:()=>c,iA:()=>i,pj:()=>m,ss:()=>o,xD:()=>l});var t=a(95344),r=a(3729),n=a(91626);let i=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{className:"relative w-full overflow-auto",children:t.jsx("table",{ref:a,className:(0,n.cn)("w-full caption-bottom text-sm",e),...s})}));i.displayName="Table";let l=r.forwardRef(({className:e,...s},a)=>t.jsx("thead",{ref:a,className:(0,n.cn)("[&_tr]:border-b",e),...s}));l.displayName="TableHeader";let d=r.forwardRef(({className:e,...s},a)=>t.jsx("tbody",{ref:a,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...s}));d.displayName="TableBody",r.forwardRef(({className:e,...s},a)=>t.jsx("tfoot",{ref:a,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let c=r.forwardRef(({className:e,...s},a)=>t.jsx("tr",{ref:a,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));c.displayName="TableRow";let o=r.forwardRef(({className:e,...s},a)=>t.jsx("th",{ref:a,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));o.displayName="TableHead";let m=r.forwardRef(({className:e,...s},a)=>t.jsx("td",{ref:a,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));m.displayName="TableCell",r.forwardRef(({className:e,...s},a)=>t.jsx("caption",{ref:a,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},25757:(e,s,a)=>{"use strict";a.d(s,{mQ:()=>M,nU:()=>D,dr:()=>T,SP:()=>S});var t=a(95344),r=a(3729),n=a(85222),i=a(98462),l=a(34504),d=a(43234),c=a(62409),o=a(3975),m=a(33183),x=a(99048),u="Tabs",[h,p]=(0,i.b)(u,[l.Pc]),f=(0,l.Pc)(),[j,v]=h(u),y=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,onValueChange:n,defaultValue:i,orientation:l="horizontal",dir:d,activationMode:h="automatic",...p}=e,f=(0,o.gm)(d),[v,y]=(0,m.T)({prop:r,onChange:n,defaultProp:i??"",caller:u});return(0,t.jsx)(j,{scope:a,baseId:(0,x.M)(),value:v,onValueChange:y,orientation:l,dir:f,activationMode:h,children:(0,t.jsx)(c.WV.div,{dir:f,"data-orientation":l,...p,ref:s})})});y.displayName=u;var g="TabsList",N=r.forwardRef((e,s)=>{let{__scopeTabs:a,loop:r=!0,...n}=e,i=v(g,a),d=f(a);return(0,t.jsx)(l.fC,{asChild:!0,...d,orientation:i.orientation,dir:i.dir,loop:r,children:(0,t.jsx)(c.WV.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:s})})});N.displayName=g;var b="TabsTrigger",w=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,disabled:i=!1,...d}=e,o=v(b,a),m=f(a),x=P(o.baseId,r),u=C(o.baseId,r),h=r===o.value;return(0,t.jsx)(l.ck,{asChild:!0,...m,focusable:!i,active:h,children:(0,t.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":u,"data-state":h?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:x,...d,ref:s,onMouseDown:(0,n.M)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(r)}),onKeyDown:(0,n.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(r)}),onFocus:(0,n.M)(e.onFocus,()=>{let e="manual"!==o.activationMode;h||i||!e||o.onValueChange(r)})})})});w.displayName=b;var k="TabsContent",Z=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:n,forceMount:i,children:l,...o}=e,m=v(k,a),x=P(m.baseId,n),u=C(m.baseId,n),h=n===m.value,p=r.useRef(h);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,t.jsx)(d.z,{present:i||h,children:({present:a})=>(0,t.jsx)(c.WV.div,{"data-state":h?"active":"inactive","data-orientation":m.orientation,role:"tabpanel","aria-labelledby":x,hidden:!a,id:u,tabIndex:0,...o,ref:s,style:{...e.style,animationDuration:p.current?"0s":void 0},children:a&&l})})});function P(e,s){return`${e}-trigger-${s}`}function C(e,s){return`${e}-content-${s}`}Z.displayName=k;var R=a(91626);let M=y,T=r.forwardRef(({className:e,...s},a)=>t.jsx(N,{ref:a,className:(0,R.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));T.displayName=N.displayName;let S=r.forwardRef(({className:e,...s},a)=>t.jsx(w,{ref:a,className:(0,R.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));S.displayName=w.displayName;let D=r.forwardRef(({className:e,...s},a)=>t.jsx(Z,{ref:a,className:(0,R.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));D.displayName=Z.displayName},73577:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Cpu",[["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"9",y:"9",width:"6",height:"6",key:"o3kz5p"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]])},73557:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]])},75203:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},77402:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]])},46064:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},74781:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>n,__esModule:()=>r,default:()=>i});let t=(0,a(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\super-admin\performance\page.tsx`),{__esModule:r,$$typeof:n}=t,i=t.default}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,7948,6671,4626,7792,2506,1729,2125,3965],()=>a(62609));module.exports=t})();