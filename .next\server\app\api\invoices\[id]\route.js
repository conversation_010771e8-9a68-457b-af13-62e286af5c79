"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/invoices/[id]/route";
exports.ids = ["app/api/invoices/[id]/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2F%5Bid%5D%2Froute&page=%2Fapi%2Finvoices%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2F%5Bid%5D%2Froute&page=%2Fapi%2Finvoices%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_invoices_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/invoices/[id]/route.ts */ \"(rsc)/./app/api/invoices/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/invoices/[id]/route\",\n        pathname: \"/api/invoices/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/invoices/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\invoices\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_invoices_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/invoices/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2F%5Bid%5D%2Froute&page=%2Fapi%2Finvoices%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/invoices/[id]/route.ts":
/*!****************************************!*\
  !*** ./app/api/invoices/[id]/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n\n\n\n\n\n// Validation schema for invoice items\nconst invoiceItemSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    name: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Name is required\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    quantity: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(1, \"Quantity must be at least 1\"),\n    unitPrice: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0, \"Unit price must be positive\"),\n    itemId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional()\n});\n// Validation schema for invoice update\nconst invoiceUpdateSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    customerId: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Customer is required\").optional(),\n    quotationId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    status: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"DRAFT\",\n        \"SENT\",\n        \"PAID\",\n        \"OVERDUE\",\n        \"CANCELLED\"\n    ]).optional(),\n    dueDate: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    terms: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    items: zod__WEBPACK_IMPORTED_MODULE_4__.array(invoiceItemSchema).min(1, \"At least one item is required\").optional(),\n    taxRate: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).max(100).optional()\n});\n// GET /api/invoices/[id] - Get single invoice\nasync function GET(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const invoice = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.findFirst({\n            where: {\n                id: params.id,\n                companyId: session.user.companyId || undefined\n            },\n            include: {\n                customer: {\n                    select: {\n                        id: true,\n                        name: true,\n                        email: true,\n                        company: true,\n                        phone: true,\n                        address: true,\n                        city: true,\n                        state: true,\n                        country: true,\n                        postalCode: true\n                    }\n                },\n                quotation: {\n                    select: {\n                        id: true,\n                        quotationNumber: true,\n                        title: true\n                    }\n                },\n                createdBy: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                },\n                items: true,\n                transactions: {\n                    orderBy: {\n                        createdAt: \"desc\"\n                    }\n                }\n            }\n        });\n        if (!invoice) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invoice not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Calculate payment totals\n        const totalPaid = invoice.transactions.reduce((sum, transaction)=>{\n            return transaction.type === \"PAYMENT\" ? sum + Number(transaction.amount) : sum;\n        }, 0);\n        const amountDue = Number(invoice.total) - totalPaid;\n        const invoiceWithTotals = {\n            ...invoice,\n            subtotal: Number(invoice.subtotal),\n            total: Number(invoice.total),\n            taxAmount: Number(invoice.taxAmount),\n            totalPaid: Math.round(totalPaid * 100) / 100,\n            amountDue: Math.round(amountDue * 100) / 100\n        };\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(invoiceWithTotals);\n    } catch (error) {\n        console.error(\"Error fetching invoice:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch invoice\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/invoices/[id] - Update invoice\nasync function PUT(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const validatedData = invoiceUpdateSchema.parse(body);\n        // Check if invoice exists and belongs to user's company\n        const existingInvoice = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.findFirst({\n            where: {\n                id: params.id,\n                companyId: session.user.companyId || undefined\n            },\n            include: {\n                items: true\n            }\n        });\n        if (!existingInvoice) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invoice not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check if invoice can be edited (not paid or cancelled)\n        if (existingInvoice.status === \"PAID\" || existingInvoice.status === \"CANCELLED\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Cannot edit paid or cancelled invoices\"\n            }, {\n                status: 400\n            });\n        }\n        // Prepare update data\n        const updateData = {\n            ...validatedData\n        };\n        if (validatedData.dueDate) {\n            updateData.dueDate = new Date(validatedData.dueDate);\n        }\n        // Recalculate totals if items are updated\n        if (validatedData.items) {\n            const subtotal = validatedData.items.reduce((sum, item)=>{\n                return sum + item.quantity * item.unitPrice;\n            }, 0);\n            const taxAmount = subtotal * (validatedData.taxRate || 0) / 100;\n            const total = subtotal + taxAmount;\n            updateData.subtotal = subtotal;\n            updateData.taxAmount = taxAmount;\n            updateData.total = total;\n        }\n        // Update invoice with items in a transaction\n        const invoice = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$transaction(async (tx)=>{\n            // Update invoice\n            const updatedInvoice = await tx.invoice.update({\n                where: {\n                    id: params.id\n                },\n                data: updateData,\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            company: true\n                        }\n                    },\n                    quotation: {\n                        select: {\n                            id: true,\n                            quotationNumber: true,\n                            title: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    items: {\n                        orderBy: {\n                            createdAt: \"asc\"\n                        }\n                    }\n                }\n            });\n            // Update items if provided\n            if (validatedData.items) {\n                // Delete existing items\n                await tx.invoiceItem.deleteMany({\n                    where: {\n                        invoiceId: params.id\n                    }\n                });\n                // Create new items\n                await tx.invoiceItem.createMany({\n                    data: validatedData.items.map((item)=>({\n                            name: item.name,\n                            description: item.description,\n                            quantity: item.quantity,\n                            unitPrice: item.unitPrice,\n                            total: item.quantity * item.unitPrice,\n                            itemId: item.itemId,\n                            invoiceId: params.id\n                        }))\n                });\n                // Fetch updated invoice with new items\n                const finalInvoice = await tx.invoice.findUnique({\n                    where: {\n                        id: params.id\n                    },\n                    include: {\n                        customer: {\n                            select: {\n                                id: true,\n                                name: true,\n                                email: true,\n                                company: true\n                            }\n                        },\n                        quotation: {\n                            select: {\n                                id: true,\n                                quotationNumber: true,\n                                title: true\n                            }\n                        },\n                        createdBy: {\n                            select: {\n                                name: true,\n                                email: true\n                            }\n                        },\n                        items: true\n                    }\n                });\n                return finalInvoice;\n            }\n            return updatedInvoice;\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(invoice);\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_5__.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"Error updating invoice:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to update invoice\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/invoices/[id] - Delete invoice\nasync function DELETE(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if invoice exists and belongs to user's company\n        const invoice = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.findFirst({\n            where: {\n                id: params.id,\n                companyId: session.user.companyId || undefined\n            },\n            include: {\n                transactions: true\n            }\n        });\n        if (!invoice) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invoice not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check if invoice has payments\n        const hasPayments = invoice.transactions.some((t)=>t.type === \"PAYMENT\");\n        if (hasPayments) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Cannot delete invoice with existing payments\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if invoice is paid\n        if (invoice.status === \"PAID\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Cannot delete paid invoices\"\n            }, {\n                status: 400\n            });\n        }\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.delete({\n            where: {\n                id: params.id\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Invoice deleted successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error deleting invoice:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to delete invoice\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/invoices/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2F%5Bid%5D%2Froute&page=%2Fapi%2Finvoices%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();