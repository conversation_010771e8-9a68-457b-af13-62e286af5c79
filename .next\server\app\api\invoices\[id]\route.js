"use strict";(()=>{var e={};e.id=316,e.ids=[316],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},70108:(e,t,i)=>{i.r(t),i.d(t,{headerHooks:()=>x,originalPathname:()=>_,patchFetch:()=>E,requestAsyncStorage:()=>f,routeModule:()=>h,serverHooks:()=>q,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>Z});var r={};i.r(r),i.d(r,{DELETE:()=>I,GET:()=>v,PUT:()=>w});var a=i(95419),n=i(69108),o=i(99678),s=i(78070),u=i(81355),l=i(3205),c=i(9108),d=i(25252),m=i(52178);let p=d.Ry({id:d.Z_().optional(),name:d.Z_().min(1,"Name is required"),description:d.Z_().optional(),quantity:d.Rx().min(1,"Quantity must be at least 1"),unitPrice:d.Rx().min(0,"Unit price must be positive"),itemId:d.Z_().optional()}),y=d.Ry({title:d.Z_().optional(),customerId:d.Z_().min(1,"Customer is required").optional(),quotationId:d.Z_().optional().nullable(),status:d.Km(["DRAFT","SENT","PAID","OVERDUE","CANCELLED"]).optional(),dueDate:d.Z_().optional(),terms:d.Z_().optional().nullable(),notes:d.Z_().optional().nullable(),items:d.IX(p).min(1,"At least one item is required").optional(),taxRate:d.Rx().min(0).max(100).optional()});async function v(e,{params:t}){try{let e=await (0,u.getServerSession)(l.L);if(!e?.user?.id)return s.Z.json({error:"Unauthorized"},{status:401});let i=await c._.invoice.findFirst({where:{id:t.id,companyId:e.user.companyId||void 0},include:{customer:{select:{id:!0,name:!0,email:!0,company:!0,phone:!0,address:!0,city:!0,state:!0,country:!0,postalCode:!0}},quotation:{select:{id:!0,quotationNumber:!0,title:!0}},createdBy:{select:{name:!0,email:!0}},items:!0,transactions:{orderBy:{createdAt:"desc"}}}});if(!i)return s.Z.json({error:"Invoice not found"},{status:404});let r=i.transactions.reduce((e,t)=>"PAYMENT"===t.type?e+Number(t.amount):e,0),a=Number(i.total)-r,n={...i,subtotal:Number(i.subtotal),total:Number(i.total),taxAmount:Number(i.taxAmount),totalPaid:Math.round(100*r)/100,amountDue:Math.round(100*a)/100};return s.Z.json(n)}catch(e){return console.error("Error fetching invoice:",e),s.Z.json({error:"Failed to fetch invoice"},{status:500})}}async function w(e,{params:t}){try{let i=await (0,u.getServerSession)(l.L);if(!i?.user?.id)return s.Z.json({error:"Unauthorized"},{status:401});let r=await e.json(),a=y.parse(r),n=await c._.invoice.findFirst({where:{id:t.id,companyId:i.user.companyId||void 0},include:{items:!0}});if(!n)return s.Z.json({error:"Invoice not found"},{status:404});if("PAID"===n.status||"CANCELLED"===n.status)return s.Z.json({error:"Cannot edit paid or cancelled invoices"},{status:400});let o={...a};if(a.dueDate&&(o.dueDate=new Date(a.dueDate)),a.items){let e=a.items.reduce((e,t)=>e+t.quantity*t.unitPrice,0),t=e*(a.taxRate||0)/100,i=e+t;o.subtotal=e,o.taxAmount=t,o.total=i}let d=await c._.$transaction(async e=>{let i=await e.invoice.update({where:{id:t.id},data:o,include:{customer:{select:{id:!0,name:!0,email:!0,company:!0}},quotation:{select:{id:!0,quotationNumber:!0,title:!0}},createdBy:{select:{name:!0,email:!0}},items:{orderBy:{createdAt:"asc"}}}});return a.items?(await e.invoiceItem.deleteMany({where:{invoiceId:t.id}}),await e.invoiceItem.createMany({data:a.items.map(e=>({name:e.name,description:e.description,quantity:e.quantity,unitPrice:e.unitPrice,total:e.quantity*e.unitPrice,itemId:e.itemId,invoiceId:t.id}))}),await e.invoice.findUnique({where:{id:t.id},include:{customer:{select:{id:!0,name:!0,email:!0,company:!0}},quotation:{select:{id:!0,quotationNumber:!0,title:!0}},createdBy:{select:{name:!0,email:!0}},items:!0}})):i});return s.Z.json(d)}catch(e){if(e instanceof m.jm)return s.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error updating invoice:",e),s.Z.json({error:"Failed to update invoice"},{status:500})}}async function I(e,{params:t}){try{let e=await (0,u.getServerSession)(l.L);if(!e?.user?.id)return s.Z.json({error:"Unauthorized"},{status:401});let i=await c._.invoice.findFirst({where:{id:t.id,companyId:e.user.companyId||void 0},include:{transactions:!0}});if(!i)return s.Z.json({error:"Invoice not found"},{status:404});if(i.transactions.some(e=>"PAYMENT"===e.type))return s.Z.json({error:"Cannot delete invoice with existing payments"},{status:400});if("PAID"===i.status)return s.Z.json({error:"Cannot delete paid invoices"},{status:400});return await c._.invoice.delete({where:{id:t.id}}),s.Z.json({message:"Invoice deleted successfully"})}catch(e){return console.error("Error deleting invoice:",e),s.Z.json({error:"Failed to delete invoice"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/invoices/[id]/route",pathname:"/api/invoices/[id]",filename:"route",bundlePath:"app/api/invoices/[id]/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\invoices\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:f,staticGenerationAsyncStorage:g,serverHooks:q,headerHooks:x,staticGenerationBailout:Z}=h,_="/api/invoices/[id]/route";function E(){return(0,o.patchFetch)({serverHooks:q,staticGenerationAsyncStorage:g})}},3205:(e,t,i)=>{i.d(t,{L:()=>l});var r=i(86485),a=i(10375),n=i(50694),o=i(6521),s=i.n(o),u=i(9108);let l={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await u._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),i=t?.companyId;if(!i&&t){let e=await u._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(i=e?.id)&&await u._.user.update({where:{id:t.id},data:{companyId:i}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await s().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await u._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:i}}catch(e){return console.error("Authentication error:",e),null}}}),(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,i)=>{i.d(t,{_:()=>a});let r=require("@prisma/client"),a=globalThis.prisma??new r.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520,5252],()=>i(70108));module.exports=r})();