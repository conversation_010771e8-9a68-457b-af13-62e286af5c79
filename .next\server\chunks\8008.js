"use strict";exports.id=8008,exports.ids=[8008],exports.modules={68008:(e,s,i)=>{i.d(s,{p:()=>u});var l=i(95344),d=i(3729),r=i(60708),a=i(55697),t=i(12374),n=i(16212),o=i(92549),c=i(1586),m=i(93601),h=i(17470),x=i(16802),j=i(44669);let p=t.Ry({firstName:t.Z_().min(1,"First name is required"),lastName:t.Z_().min(1,"Last name is required"),email:t.Z_().email("Invalid email address"),phone:t.Z_().optional(),companyName:t.Z_().optional(),title:t.Z_().optional(),website:t.Z_().url().optional().or(t.i0("")),source:t.Km(["WEBSITE","REFERRAL","SOCIAL_MEDIA","EMAIL_CAMPAIGN","COLD_CALL","TRADE_SHOW","PARTNER","OTHER"]).default("OTHER"),status:t.Km(["NEW","CONTACTED","QUALIFIED","PROPOSAL","NEGOTIATION","CLOSED_WON","CLOSED_LOST"]).default("NEW"),priority:t.Km(["LOW","MEDIUM","HIGH","URGENT"]).default("MEDIUM"),address:t.Z_().optional(),city:t.Z_().optional(),state:t.Z_().optional(),country:t.Z_().optional(),postalCode:t.Z_().optional(),industry:t.Z_().optional(),companySize:t.Z_().optional(),budget:t.Rx().min(0).optional(),timeline:t.Z_().optional(),description:t.Z_().optional()});function u({isOpen:e,onClose:s,onSuccess:i,lead:t,mode:u}){let[N,y]=(0,d.useState)(!1),{register:v,handleSubmit:g,formState:{errors:f},reset:E,setValue:I,watch:_}=(0,r.cI)({resolver:(0,a.F)(p),defaultValues:{source:"OTHER",status:"NEW",priority:"MEDIUM"}});(0,d.useEffect)(()=>{t&&"edit"===u?E({firstName:t.firstName,lastName:t.lastName,email:t.email,phone:t.phone||"",companyName:t.companyName||"",title:t.title||"",website:t.website||"",source:t.source,status:t.status,priority:t.priority,address:t.address||"",city:t.city||"",state:t.state||"",country:t.country||"",postalCode:t.postalCode||"",industry:t.industry||"",companySize:t.companySize||"",budget:t.budget||void 0,timeline:t.timeline||"",description:t.description||""}):E({source:"OTHER",status:"NEW",priority:"MEDIUM"})},[t,u,E]);let C=async e=>{y(!0);try{let l="edit"===u?`/api/leads/${t?.id}`:"/api/leads",d=await fetch(l,{method:"edit"===u?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!d.ok){let e=await d.json();throw Error(e.error||"Failed to save lead")}j.toast.success(`Lead ${"edit"===u?"updated":"created"} successfully`),i(),s()}catch(e){j.toast.error(e instanceof Error?e.message:"An error occurred")}finally{y(!1)}},b=()=>{E(),s()};return l.jsx(x.Vq,{open:e,onOpenChange:b,children:(0,l.jsxs)(x.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[l.jsx(x.fK,{children:l.jsx(x.$N,{children:"edit"===u?"Edit Lead":"Create New Lead"})}),(0,l.jsxs)("form",{onSubmit:g(C),className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[l.jsx("h3",{className:"text-lg font-medium",children:"Personal Information"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"firstName",children:"First Name *"}),l.jsx(o.I,{id:"firstName",...v("firstName"),className:f.firstName?"border-red-500":""}),f.firstName&&l.jsx("p",{className:"text-red-500 text-sm mt-1",children:f.firstName.message})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"lastName",children:"Last Name *"}),l.jsx(o.I,{id:"lastName",...v("lastName"),className:f.lastName?"border-red-500":""}),f.lastName&&l.jsx("p",{className:"text-red-500 text-sm mt-1",children:f.lastName.message})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"email",children:"Email *"}),l.jsx(o.I,{id:"email",type:"email",...v("email"),className:f.email?"border-red-500":""}),f.email&&l.jsx("p",{className:"text-red-500 text-sm mt-1",children:f.email.message})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"phone",children:"Phone"}),l.jsx(o.I,{id:"phone",...v("phone")})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[l.jsx("h3",{className:"text-lg font-medium",children:"Company Information"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"companyName",children:"Company Name"}),l.jsx(o.I,{id:"companyName",...v("companyName")})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"title",children:"Job Title"}),l.jsx(o.I,{id:"title",...v("title")})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"website",children:"Website"}),l.jsx(o.I,{id:"website",...v("website"),placeholder:"https://example.com"}),f.website&&l.jsx("p",{className:"text-red-500 text-sm mt-1",children:f.website.message})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"industry",children:"Industry"}),l.jsx(o.I,{id:"industry",...v("industry")})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[l.jsx("h3",{className:"text-lg font-medium",children:"Lead Details"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"source",children:"Source *"}),(0,l.jsxs)(h.Ph,{onValueChange:e=>I("source",e),children:[l.jsx(h.i4,{children:l.jsx(h.ki,{placeholder:"Select source"})}),(0,l.jsxs)(h.Bw,{children:[l.jsx(h.Ql,{value:"WEBSITE",children:"Website"}),l.jsx(h.Ql,{value:"REFERRAL",children:"Referral"}),l.jsx(h.Ql,{value:"SOCIAL_MEDIA",children:"Social Media"}),l.jsx(h.Ql,{value:"EMAIL_CAMPAIGN",children:"Email Campaign"}),l.jsx(h.Ql,{value:"COLD_CALL",children:"Cold Call"}),l.jsx(h.Ql,{value:"TRADE_SHOW",children:"Trade Show"}),l.jsx(h.Ql,{value:"PARTNER",children:"Partner"}),l.jsx(h.Ql,{value:"OTHER",children:"Other"})]})]})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"status",children:"Status *"}),(0,l.jsxs)(h.Ph,{onValueChange:e=>I("status",e),children:[l.jsx(h.i4,{children:l.jsx(h.ki,{placeholder:"Select status"})}),(0,l.jsxs)(h.Bw,{children:[l.jsx(h.Ql,{value:"NEW",children:"New"}),l.jsx(h.Ql,{value:"CONTACTED",children:"Contacted"}),l.jsx(h.Ql,{value:"QUALIFIED",children:"Qualified"}),l.jsx(h.Ql,{value:"PROPOSAL",children:"Proposal"}),l.jsx(h.Ql,{value:"NEGOTIATION",children:"Negotiation"}),l.jsx(h.Ql,{value:"CLOSED_WON",children:"Closed Won"}),l.jsx(h.Ql,{value:"CLOSED_LOST",children:"Closed Lost"})]})]})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"priority",children:"Priority *"}),(0,l.jsxs)(h.Ph,{onValueChange:e=>I("priority",e),children:[l.jsx(h.i4,{children:l.jsx(h.ki,{placeholder:"Select priority"})}),(0,l.jsxs)(h.Bw,{children:[l.jsx(h.Ql,{value:"LOW",children:"Low"}),l.jsx(h.Ql,{value:"MEDIUM",children:"Medium"}),l.jsx(h.Ql,{value:"HIGH",children:"High"}),l.jsx(h.Ql,{value:"URGENT",children:"Urgent"})]})]})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[l.jsx("h3",{className:"text-lg font-medium",children:"Business Information"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"companySize",children:"Company Size"}),l.jsx(o.I,{id:"companySize",...v("companySize"),placeholder:"e.g., 1-10, 11-50, 51-200"})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"budget",children:"Budget ($)"}),l.jsx(o.I,{id:"budget",type:"number",min:"0",step:"0.01",...v("budget",{valueAsNumber:!0}),placeholder:"0.00"})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"timeline",children:"Timeline"}),l.jsx(o.I,{id:"timeline",...v("timeline"),placeholder:"e.g., Q1 2024, 3-6 months"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[l.jsx("h3",{className:"text-lg font-medium",children:"Address Information"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"address",children:"Address"}),l.jsx(o.I,{id:"address",...v("address"),placeholder:"Street address"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"city",children:"City"}),l.jsx(o.I,{id:"city",...v("city")})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"state",children:"State/Province"}),l.jsx(o.I,{id:"state",...v("state")})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"postalCode",children:"Postal Code"}),l.jsx(o.I,{id:"postalCode",...v("postalCode")})]})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"country",children:"Country"}),l.jsx(o.I,{id:"country",...v("country")})]})]})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"description",children:"Description"}),l.jsx(m.g,{id:"description",...v("description"),rows:3,placeholder:"Additional notes about this lead..."})]}),(0,l.jsxs)("div",{className:"flex justify-end space-x-4 pt-4",children:[l.jsx(n.z,{type:"button",variant:"outline",onClick:b,children:"Cancel"}),l.jsx(n.z,{type:"submit",disabled:N,children:N?"Saving...":"edit"===u?"Update Lead":"Create Lead"})]})]})]})})}},93601:(e,s,i)=>{i.d(s,{g:()=>a});var l=i(95344),d=i(3729),r=i(91626);let a=d.forwardRef(({className:e,...s},i)=>l.jsx("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:i,...s}));a.displayName="Textarea"}};