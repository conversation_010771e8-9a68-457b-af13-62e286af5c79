"use strict";(()=>{var e={};e.id=5337,e.ids=[5337],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},1535:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>I,originalPathname:()=>E,patchFetch:()=>T,requestAsyncStorage:()=>y,routeModule:()=>g,serverHooks:()=>w,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>v});var a={};r.r(a),r.d(a,{POST:()=>p});var s=r(95419),o=r(69108),i=r(99678),n=r(78070),l=r(81355),c=r(3205),u=r(9108);let d={companySize:{STARTUP:5,SMALL:10,MEDIUM:20,LARGE:25,ENTERPRISE:30},industry:{TECHNOLOGY:25,FINANCE:20,HEALTHCARE:20,MANUFACTURING:15,RETAIL:10,OTHER:5},hasPhone:10,hasWebsite:5,hasCompanyEmail:5,activityCount:{0:0,1:5,2:10,3:15,4:20,5:25},recentActivity:10,hasBudget:15,hasTimeline:10};async function p(e){try{let t=await (0,l.getServerSession)(c.L);if(!t?.user?.id||!t?.user?.companyId)return n.Z.json({error:"Unauthorized"},{status:401});let{action:r,leadIds:a}=await e.json();if("recalculate"===r)return await m(t.user.companyId,t.user.id,a);if("analyze"===r)return await h(t.user.companyId);return n.Z.json({error:"Invalid action"},{status:400})}catch(e){return console.error("Error in bulk scoring operation:",e),n.Z.json({error:"Failed to perform bulk scoring operation"},{status:500})}}async function m(e,t,r){try{let a={companyId:e};r&&r.length>0&&(a.id={in:r});let s=await u._.lead.findMany({where:a,include:{activities:{orderBy:{createdAt:"desc"},take:10},_count:{select:{activities:!0}}}}),o=[],i=[];for(let r of s){let a=function(e){let t=0;e.companySize&&d.companySize[e.companySize]&&(t+=d.companySize[e.companySize]),e.industry&&d.industry[e.industry]&&(t+=d.industry[e.industry]),e.phone&&(t+=d.hasPhone),e.website&&(t+=d.hasWebsite),e.email&&e.companyName&&e.email.includes(e.companyName.toLowerCase())&&(t+=d.hasCompanyEmail);let r=Math.min(e._count.activities,5);if(t+=d.activityCount[r]||0,e.activities.length>0){let r=new Date(e.activities[0].createdAt);(Date.now()-r.getTime())/864e5<=7&&(t+=d.recentActivity)}return e.budget&&e.budget>0&&(t+=d.hasBudget),e.timeline&&(t+=d.hasTimeline),Math.min(t,100)}(r);a!==r.score&&(o.push({id:r.id,score:a}),i.push({leadId:r.id,previousScore:r.score,newScore:a,changeReason:"Bulk score recalculation",isManual:!1,createdById:t,companyId:e}))}return o.length>0&&await Promise.all([...o.map(e=>u._.lead.update({where:{id:e.id},data:{score:e.score,updatedAt:new Date}})),u._.leadScoreHistory.createMany({data:i})]),n.Z.json({message:"Scores recalculated successfully",updatedCount:o.length,totalProcessed:s.length,updates:o.map(e=>({leadId:e.id,newScore:e.score}))})}catch(e){throw console.error("Error recalculating scores:",e),e}}async function h(e){try{let t=await u._.lead.findMany({where:{companyId:e},select:{id:!0,score:!0,status:!0,createdAt:!0}}),r=t.length,a=r>0?Math.round(t.reduce((e,t)=>e+t.score,0)/r):0,s={HOT:t.filter(e=>e.score>=70).length,WARM:t.filter(e=>e.score>=40&&e.score<70).length,COLD:t.filter(e=>e.score<40).length},o={"0-20":t.filter(e=>e.score>=0&&e.score<20).length,"20-40":t.filter(e=>e.score>=20&&e.score<40).length,"40-60":t.filter(e=>e.score>=40&&e.score<60).length,"60-80":t.filter(e=>e.score>=60&&e.score<80).length,"80-100":t.filter(e=>e.score>=80&&e.score<=100).length},i={};for(let e of[...new Set(t.map(e=>e.status))]){let r=t.filter(t=>t.status===e);i[e]={count:r.length,averageScore:r.length>0?Math.round(r.reduce((e,t)=>e+t.score,0)/r.length):0}}let l=[...t].sort((e,t)=>t.score-e.score),c=l.slice(0,10),d=l.slice(-10).reverse(),p=new Date;p.setDate(p.getDate()-30);let m=t.filter(e=>new Date(e.createdAt)>=p),h=m.length>0?Math.round(m.reduce((e,t)=>e+t.score,0)/m.length):0;return n.Z.json({summary:{totalLeads:r,averageScore:a,recentLeads:m.length,recentAverageScore:h},temperatureDistribution:s,scoreRanges:o,statusAnalysis:i,topPerformers:c.map(e=>({id:e.id,score:e.score,status:e.status})),bottomPerformers:d.map(e=>({id:e.id,score:e.score,status:e.status})),recommendations:function(e,t){let r=[];t.COLD>t.HOT&&r.push({type:"ENGAGEMENT",priority:"HIGH",message:`You have ${t.COLD} cold leads. Focus on re-engagement campaigns.`,action:"Launch re-engagement campaign"}),t.HOT>0&&r.push({type:"CONVERSION",priority:"URGENT",message:`You have ${t.HOT} hot leads ready for conversion.`,action:"Prioritize hot leads for immediate follow-up"});let a=e.filter(e=>!e.activities||0===e.activities.length).length;return a>0&&r.push({type:"ACTIVITY",priority:"HIGH",message:`${a} leads have no recorded activities.`,action:"Schedule initial contact activities"}),r}(t,s)})}catch(e){throw console.error("Error analyzing lead scores:",e),e}}let g=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/leads/scoring/bulk/route",pathname:"/api/leads/scoring/bulk",filename:"route",bundlePath:"app/api/leads/scoring/bulk/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\leads\\scoring\\bulk\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:y,staticGenerationAsyncStorage:f,serverHooks:w,headerHooks:I,staticGenerationBailout:v}=g,E="/api/leads/scoring/bulk/route";function T(){return(0,i.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:f})}},3205:(e,t,r)=>{r.d(t,{L:()=>c});var a=r(86485),s=r(10375),o=r(50694),i=r(6521),n=r.n(i),l=r(9108);let c={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await n().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>s});let a=require("@prisma/client"),s=globalThis.prisma??new a.PrismaClient}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,6521,2455,4520],()=>r(1535));module.exports=a})();