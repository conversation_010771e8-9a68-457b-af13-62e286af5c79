{"version": 3, "file": "class-validator.umd.js", "sources": ["../src/class-validator.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport {\n  ClassConstructor,\n  ClassTransformOptions,\n  plainToClass,\n} from 'class-transformer';\nimport {\n  ValidationError,\n  ValidatorOptions,\n  validate,\n  validateSync,\n} from 'class-validator';\nimport { FieldErrors, Resolver } from 'react-hook-form';\n\nfunction parseErrorSchema(\n  errors: ValidationError[],\n  validateAllFieldCriteria: boolean,\n  parsedErrors: FieldErrors = {},\n  path = '',\n) {\n  return errors.reduce((acc, error) => {\n    const _path = path ? `${path}.${error.property}` : error.property;\n\n    if (error.constraints) {\n      const key = Object.keys(error.constraints)[0];\n      acc[_path] = {\n        type: key,\n        message: error.constraints[key],\n      };\n\n      const _e = acc[_path];\n      if (validateAllFieldCriteria && _e) {\n        Object.assign(_e, { types: error.constraints });\n      }\n    }\n\n    if (error.children && error.children.length) {\n      parseErrorSchema(error.children, validateAllFieldCriteria, acc, _path);\n    }\n\n    return acc;\n  }, parsedErrors);\n}\n\n/**\n * Creates a resolver for react-hook-form using class-validator schema validation\n * @param {ClassConstructor<Schema>} schema - The class-validator schema to validate against\n * @param {Object} schemaOptions - Additional schema validation options\n * @param {Object} resolverOptions - Additional resolver configuration\n * @param {string} [resolverOptions.mode='async'] - Validation mode\n * @returns {Resolver<Schema>} A resolver function compatible with react-hook-form\n * @example\n * class Schema {\n *   @Matches(/^\\w+$/)\n *   @Length(3, 30)\n *   username: string;\n *   age: number\n * }\n *\n * useForm({\n *   resolver: classValidatorResolver(Schema)\n * });\n */\nexport function classValidatorResolver<Schema extends Record<string, any>>(\n  schema: ClassConstructor<Schema>,\n  schemaOptions: {\n    validator?: ValidatorOptions;\n    transformer?: ClassTransformOptions;\n  } = {},\n  resolverOptions: { mode?: 'async' | 'sync'; raw?: boolean } = {},\n): Resolver<Schema> {\n  return async (values, _, options) => {\n    const { transformer, validator } = schemaOptions;\n    const data = plainToClass(schema, values, transformer);\n\n    const rawErrors = await (resolverOptions.mode === 'sync'\n      ? validateSync\n      : validate)(data, validator);\n\n    if (rawErrors.length) {\n      return {\n        values: {},\n        errors: toNestErrors(\n          parseErrorSchema(\n            rawErrors,\n            !options.shouldUseNativeValidation &&\n              options.criteriaMode === 'all',\n          ),\n          options,\n        ),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return {\n      values: resolverOptions.raw ? Object.assign({}, values) : data,\n      errors: {},\n    };\n  };\n}\n"], "names": ["parseErrorSchema", "errors", "validateAllFieldCriteria", "parsedErrors", "path", "reduce", "acc", "error", "_path", "property", "constraints", "key", "Object", "keys", "type", "message", "_e", "assign", "types", "children", "length", "schema", "schemaOptions", "resolverOptions", "values", "_", "options", "validator", "data", "plainToClass", "transformer", "Promise", "resolve", "mode", "validateSync", "validate", "then", "rawErrors", "toNestErrors", "shouldUseNativeValidation", "criteriaMode", "validateFieldsNatively", "raw", "e", "reject"], "mappings": "0cAcA,SAASA,EACPC,EACAC,EACAC,EACAC,GAEA,YAHA,IAAAD,IAAAA,EAA4B,CAAE,YAC9BC,IAAAA,EAAO,IAEAH,EAAOI,OAAO,SAACC,EAAKC,GACzB,IAAMC,EAAQJ,EAAUA,EAAI,IAAIG,EAAME,SAAaF,EAAME,SAEzD,GAAIF,EAAMG,YAAa,CACrB,IAAMC,EAAMC,OAAOC,KAAKN,EAAMG,aAAa,GAC3CJ,EAAIE,GAAS,CACXM,KAAMH,EACNI,QAASR,EAAMG,YAAYC,IAG7B,IAAMK,EAAKV,EAAIE,GACXN,GAA4Bc,GAC9BJ,OAAOK,OAAOD,EAAI,CAAEE,MAAOX,EAAMG,aAErC,CAMA,OAJIH,EAAMY,UAAYZ,EAAMY,SAASC,QACnCpB,EAAiBO,EAAMY,SAAUjB,EAA0BI,EAAKE,GAG3DF,CACT,EAAGH,EACL,0BAqBM,SACJkB,EACAC,EAIAC,GAEA,gBANAD,IAAAA,EAGI,CAAE,QACN,IAAAC,IAAAA,EAA8D,CAAE,GAEhE,SAAcC,EAAQC,EAAGC,OACvB,IAAqBC,EAAcL,EAAdK,UACfC,EAAOC,EAAYA,aAACR,EAAQG,EADCF,EAA3BQ,aAC+C,OAAAC,QAAAC,SAEL,SAAzBT,EAAgBU,KACrCC,eACAC,EAAAA,UAAUP,EAAMD,IAAUS,KAFxBC,SAAAA,GAIN,OAAIA,EAAUjB,OACL,CACLI,OAAQ,CAAA,EACRvB,OAAQqC,eACNtC,EACEqC,GACCX,EAAQa,2BACkB,QAAzBb,EAAQc,cAEZd,KAKNA,EAAQa,2BAA6BE,EAAsBA,uBAAC,CAAE,EAAEf,GAEzD,CACLF,OAAQD,EAAgBmB,IAAM9B,OAAOK,OAAO,CAAE,EAAEO,GAAUI,EAC1D3B,OAAQ,CAAA,GACR,EACJ,CAAC,MAAA0C,GAAAZ,OAAAA,QAAAa,OAAAD,EACH,CAAA,CAAA"}