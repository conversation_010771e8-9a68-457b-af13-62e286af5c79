{"name": "@types/d3-ease", "version": "3.0.2", "description": "TypeScript definitions for d3-ease", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/d3-ease", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/tomwanzek"}, {"name": "<PERSON>", "githubUsername": "gust<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/gustavderdrache"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "<PERSON>", "githubUsername": "Methuselah96", "url": "https://github.com/Methuselah96"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/d3-ease"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "2995c518f8de4fa6f2abb2f13065cb4fe65acaea9422f9883b24414ef50cd1ab", "typeScriptVersion": "4.5"}